<?php
// --- PHP Code Block ---
// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}

require_once 'config/database.php'; // Ensure this path is correct
require_once 'access_control.php'; // Include access control functions

// Check if user has full access to this page
$hasFullAccess = hasFullAccess('ILCDB');

// **** START ILCDB FILTER ****
$project_filter_value = 'ILCDB'; // Define the filter value for BOTH activities and participants
$target_category_filter = 'ILCDB'; // Filter targets by this category
// **** END ILCDB FILTER ****

// --- Determine Active Tab ---
$active_tab = isset($_GET['tab']) && in_array($_GET['tab'], ['activities', 'participants', 'reports', 'tech4ed']) ? $_GET['tab'] : 'activities'; // Default to activities

// --- Determine Selected Report Year ---
$selected_report_year = isset($_GET['year']) ? filter_var($_GET['year'], FILTER_VALIDATE_INT) : (int)date('Y'); // Get year from URL or default to current
// Validate the selected year is within reasonable range
if ($selected_report_year === false || $selected_report_year <= 0 || $selected_report_year < 2000 || $selected_report_year > 2100) {
    $selected_report_year = (int)date('Y'); // Reset to current year if invalid
}


// Check database connection
if (!$conn) {
    error_log("Database connection failed in ilcdb.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
} else {
    $db_connection_error = null;
     // Set charset for the connection
    mysqli_set_charset($conn, "utf8mb4");
}

// --- Fetch list of distinct years from targets AND activities for dropdown ---
$available_years = [];
if (!$db_connection_error) {
    // Years from Targets (exclude NULL, 0, and invalid years)
    $target_year_sql = "SELECT DISTINCT year FROM targets WHERE category = ? AND year IS NOT NULL AND year > 0 AND year >= 2000 AND year <= 2100 ORDER BY year DESC";
    $stmt_target_years = mysqli_prepare($conn, $target_year_sql);
    if ($stmt_target_years) {
        mysqli_stmt_bind_param($stmt_target_years, "s", $target_category_filter);
        if (mysqli_stmt_execute($stmt_target_years)) {
            $result_target_years = mysqli_stmt_get_result($stmt_target_years);
            while ($row = mysqli_fetch_assoc($result_target_years)) {
                $year = (int)$row['year'];
                if ($year > 0 && $year >= 2000 && $year <= 2100) { // Double-check valid year range
                    $available_years[] = $year;
                }
            }
        } else { error_log("Error fetching target years: " . mysqli_stmt_error($stmt_target_years)); }
        mysqli_stmt_close($stmt_target_years);
    } else { error_log("Error preparing target years query: " . mysqli_error($conn)); }

    // Years from Activities (where start date is not null and year is valid)
    $activity_year_sql = "SELECT DISTINCT YEAR(start) as activity_year FROM tblactivity WHERE project = ? AND start IS NOT NULL AND YEAR(start) > 0 AND YEAR(start) >= 2000 AND YEAR(start) <= 2100 ORDER BY activity_year DESC";
    $stmt_activity_years = mysqli_prepare($conn, $activity_year_sql);
     if ($stmt_activity_years) {
        mysqli_stmt_bind_param($stmt_activity_years, "s", $project_filter_value);
         if (mysqli_stmt_execute($stmt_activity_years)) {
             $result_activity_years = mysqli_stmt_get_result($stmt_activity_years);
             while ($row = mysqli_fetch_assoc($result_activity_years)) {
                 $year = (int)$row['activity_year'];
                 if ($year > 0 && $year >= 2000 && $year <= 2100) { // Double-check valid year range
                     $available_years[] = $year;
                 }
             }
         } else { error_log("Error fetching activity years: " . mysqli_stmt_error($stmt_activity_years)); }
         mysqli_stmt_close($stmt_activity_years);
     } else { error_log("Error preparing activity years query: " . mysqli_error($conn)); }

    // Add current year if missing and ensure defaults exist if query fails
    $current_actual_year = (int)date('Y');
    $available_years[] = $current_actual_year; // Ensure current year is always an option
    $available_years = array_unique($available_years); // Remove duplicates
    $available_years = array_filter($available_years, function($year) {
        return $year > 0 && $year >= 2000 && $year <= 2100; // Final filter to ensure all years are valid
    });
    rsort($available_years); // Sort descending

    if (empty($available_years)) {
       $available_years = [(int)date('Y'), (int)date('Y')-1]; // Fallback if absolutely no data
    }

} else {
     $available_years = [(int)date('Y'), (int)date('Y')-1]; // Fallback on DB error
}

// Validate selected year or default
if (!in_array($selected_report_year, $available_years)) {
    $selected_report_year = !empty($available_years) ? $available_years[0] : (int)date('Y'); // Default to the latest available year or current year
}


// --- View Parameter (always 'month' now for Stats) ---
$current_view = 'month';

// --- Date Calculations (Only needed if view is 'month' for Stats) ---
$current_month_start = null; $current_month_end = null; $prev_month_start = null; $prev_month_end = null;
if ($current_view === 'month') {
    $current_month_start = date('Y-m-01 00:00:00');
    $current_month_end = date('Y-m-t 23:59:59');
    $prev_month_timestamp = strtotime('-1 month', strtotime($current_month_start));
    $prev_month_start = date('Y-m-01 00:00:00', $prev_month_timestamp);
    $prev_month_end = date('Y-m-t 23:59:59', $prev_month_timestamp);
}

// --- Helper Function for Calculating Change ---
function calculateChange($current, $previous) {
     $diff = $current - $previous; $percentage = 0; $direction = 'no_change'; $icon_class = 'fa-minus'; $color_class = 'text-neutral';
    if ($previous > 0) { $percentage = round(($diff / $previous) * 100); }
    elseif ($current > 0 && $previous == 0) { $percentage = 100; $direction = 'increase'; }
    $formatted_diff = number_format(abs($diff)); $current_month = date('F'); $prev_month_name = date('F', strtotime('-1 month'));
    if ($diff > 0) { $direction = 'increase'; $icon_class = 'fa-arrow-up'; $color_class = 'text-green'; $text = "{$formatted_diff} ({$percentage}%) more in {$current_month} vs {$prev_month_name}"; }
    elseif ($diff < 0) { $direction = 'decrease'; $icon_class = 'fa-arrow-down'; $color_class = 'text-red'; $text = "{$formatted_diff} (" . abs($percentage) . "%) less in {$current_month} vs {$prev_month_name}"; }
    else { if ($current == 0 && $previous == 0) { $text = "No activity in {$current_month} or {$prev_month_name}"; } else { $text = "No change in {$current_month} vs {$prev_month_name}"; } }
    return [ 'difference' => $diff, 'percentage' => $percentage, 'direction' => $direction, 'icon_class' => $icon_class, 'color_class' => $color_class, 'text' => $text ];
}

// --- Fetch Statistics Data (Filtered for ILCDB Activities) ---
$stats_data = []; $stats_data_current_month = []; $stats_data_previous = []; $stats = []; $stat_queries_ok = true;
if (!$db_connection_error) {
    // Keep the existing fetchStatValue function definition here
    function fetchStatValue($conn, $sql, $params = [], $types = '') {
        $value = 0; $stmt = mysqli_prepare($conn, $sql);
        if ($stmt) { if (!empty($params) && !empty($types)) { if (count($params) !== strlen($types)) { error_log("Stat Fetch Mismatch Error: P".count($params)."!=T".strlen($types)."|SQL:".$sql); mysqli_stmt_close($stmt); $GLOBALS['stat_queries_ok'] = false; return 0; } mysqli_stmt_bind_param($stmt, $types, ...$params); }
            if (mysqli_stmt_execute($stmt)) { $result = mysqli_stmt_get_result($stmt); if ($result) { $row = mysqli_fetch_assoc($result); $value = $row['total'] ?? 0; mysqli_free_result($result); } else { error_log("Stat Fetch Result Error: ".mysqli_stmt_error($stmt)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } }
            else { error_log("Stat Execute Error: ".mysqli_stmt_error($stmt)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } mysqli_stmt_close($stmt);
        } else { error_log("Stat Prepare Error: ".mysqli_error($conn)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } return is_numeric($value) ? (int)$value : 0;
    }
    $project_filter_where = " WHERE project = ?"; $project_filter_and = " AND project = ?";
    $count_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE start >= ? AND start <= ?" . $project_filter_and;
    $count_distinct_sql_monthly = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != '' AND start >= ? AND start <= ?" . $project_filter_and;
    $sum_sql_monthly = "SELECT SUM(%s) AS total FROM tblactivity WHERE start >= ? AND start <= ?" . $project_filter_and;
    $count_district_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ? AND start >= ? AND start <= ?" . $project_filter_and;
    $types_ss_project = 'sss'; $types_sss_project = 'ssss'; $count_sql_all = "SELECT COUNT(*) AS total FROM tblactivity" . $project_filter_where;
    $count_distinct_sql_all = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != ''" . $project_filter_and;
    $sum_sql_all = "SELECT SUM(%s) AS total FROM tblactivity" . $project_filter_where;
    $count_district_sql_all = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ?" . $project_filter_and;
    $types_s_project = 's'; $types_ss_project_dist = 'ss'; $district1_name = 'District 1 (Siargao Island)'; $district2_name = 'District 2 (Mainland)';
    $params_project = [$project_filter_value];
    $stats_data['activities'] = fetchStatValue($conn, $count_sql_all, $params_project, $types_s_project);
    $stats_data['participants'] = fetchStatValue($conn, sprintf($sum_sql_all, 'participants'), $params_project, $types_s_project);
    $stats_data['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'sector', 'sector', 'sector'), $params_project, $types_s_project);
    $stats_data['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'agency', 'agency', 'agency'), $params_project, $types_s_project);
    $stats_data['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'municipality', 'municipality', 'municipality'), $params_project, $types_s_project);
    $stats_data['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'barangay', 'barangay', 'barangay'), $params_project, $types_s_project);
    $stats_data['district1'] = fetchStatValue($conn, $count_district_sql_all, [$district1_name, $project_filter_value], $types_ss_project_dist);
    $stats_data['district2'] = fetchStatValue($conn, $count_district_sql_all, [$district2_name, $project_filter_value], $types_ss_project_dist);
    $date_params_current = [$current_month_start, $current_month_end]; $params_current_project = [...$date_params_current, $project_filter_value];
    $stats_data_current_month['activities'] = fetchStatValue($conn, $count_sql_monthly, $params_current_project, $types_ss_project);
    $stats_data_current_month['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $params_current_project, $types_ss_project);
    $stats_data_current_month['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $params_current_project, $types_ss_project);
    $stats_data_current_month['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $params_current_project, $types_ss_project);
    $stats_data_current_month['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $params_current_project, $types_ss_project);
    $stats_data_current_month['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $params_current_project, $types_ss_project);
    $stats_data_current_month['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_current, $project_filter_value], $types_sss_project);
    $stats_data_current_month['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_current, $project_filter_value], $types_sss_project);
    $date_params_previous = [$prev_month_start, $prev_month_end]; $params_previous_project = [...$date_params_previous, $project_filter_value];
    $stats_data_previous['activities'] = fetchStatValue($conn, $count_sql_monthly, $params_previous_project, $types_ss_project);
    $stats_data_previous['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $params_previous_project, $types_ss_project);
    $stats_data_previous['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $params_previous_project, $types_ss_project);
    $stats_data_previous['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $params_previous_project, $types_ss_project);
    $stats_data_previous['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $params_previous_project, $types_ss_project);
    $stats_data_previous['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $params_previous_project, $types_ss_project);
    $stats_data_previous['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_previous, $project_filter_value], $types_sss_project);
    $stats_data_previous['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_previous, $project_filter_value], $types_sss_project);
}
if ($stat_queries_ok) {
    // Prepare $stats array with keys and all data needed for the modal
    $change_activities = calculateChange($stats_data_current_month['activities'], $stats_data_previous['activities']);
    $stats[] = [
        'key' => 'activities', 'icon' => 'fas fa-tasks', 'title' => 'Total Activities',
        'value' => number_format($stats_data['activities']),
        'current_month_value' => number_format($stats_data_current_month['activities']),
        'prev_month_value' => number_format($stats_data_previous['activities']),
        'insight' => $change_activities
    ];
    $change_participants = calculateChange($stats_data_current_month['participants'], $stats_data_previous['participants']);
    $stats[] = [
        'key' => 'participants', 'icon' => 'fas fa-users', 'title' => 'Total Participants',
        'value' => number_format($stats_data['participants']),
        'current_month_value' => number_format($stats_data_current_month['participants']),
        'prev_month_value' => number_format($stats_data_previous['participants']),
        'insight' => $change_participants
    ];
     $change_sectors = calculateChange($stats_data_current_month['sectors'], $stats_data_previous['sectors']);
    $stats[] = [
        'key' => 'sectors', 'icon' => 'fas fa-tags', 'title' => 'Total Unique Sectors',
        'value' => number_format($stats_data['sectors']),
        'current_month_value' => number_format($stats_data_current_month['sectors']),
        'prev_month_value' => number_format($stats_data_previous['sectors']),
        'insight' => $change_sectors
    ];
    $change_agencies = calculateChange($stats_data_current_month['agencies'], $stats_data_previous['agencies']);
    $stats[] = [
        'key' => 'agencies', 'icon' => 'fas fa-building', 'title' => 'Total Unique Agencies',
        'value' => number_format($stats_data['agencies']),
        'current_month_value' => number_format($stats_data_current_month['agencies']),
        'prev_month_value' => number_format($stats_data_previous['agencies']),
        'insight' => $change_agencies
    ];
    $change_municipalities = calculateChange($stats_data_current_month['municipalities'], $stats_data_previous['municipalities']);
    $stats[] = [
        'key' => 'municipalities', 'icon' => 'fas fa-map-marker-alt', 'title' => 'Total Unique Municipalities',
        'value' => number_format($stats_data['municipalities']),
        'current_month_value' => number_format($stats_data_current_month['municipalities']),
        'prev_month_value' => number_format($stats_data_previous['municipalities']),
        'insight' => $change_municipalities
    ];
    $change_barangays = calculateChange($stats_data_current_month['barangays'], $stats_data_previous['barangays']);
    $stats[] = [
        'key' => 'barangays', 'icon' => 'fas fa-map-pin', 'title' => 'Total Unique Barangays',
        'value' => number_format($stats_data['barangays']),
        'current_month_value' => number_format($stats_data_current_month['barangays']),
        'prev_month_value' => number_format($stats_data_previous['barangays']),
        'insight' => $change_barangays
    ];
    $change_district1 = calculateChange($stats_data_current_month['district1'], $stats_data_previous['district1']);
    $stats[] = [
        'key' => 'district1', 'icon' => 'fas fa-map', 'title' => 'Total District 1 Activities',
        'value' => number_format($stats_data['district1']),
        'current_month_value' => number_format($stats_data_current_month['district1']),
        'prev_month_value' => number_format($stats_data_previous['district1']),
        'insight' => $change_district1
    ];
    $change_district2 = calculateChange($stats_data_current_month['district2'], $stats_data_previous['district2']);
    $stats[] = [
        'key' => 'district2', 'icon' => 'fas fa-map-signs', 'title' => 'Total District 2 Activities',
        'value' => number_format($stats_data['district2']),
        'current_month_value' => number_format($stats_data_current_month['district2']),
        'prev_month_value' => number_format($stats_data_previous['district2']),
        'insight' => $change_district2
    ];
} else {
     $stats[] = [
        'key' => 'error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Stats Error',
        'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Could not load statistics.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
    ];
}

// === ACTIVITIES TAB DATA ===
$filter_columns_activity = ['year', 'indicator', 'sector', 'municipality', 'barangay']; $search_term_activity = isset($_GET['search']) ? trim($_GET['search']) : ''; $where_clauses_activity = ["project = ?"]; $query_params_activity = [$project_filter_value]; $query_param_types_activity = 's';
if (!empty($search_term_activity)) { $like_term_activity = '%' . $search_term_activity . '%'; $searchable_columns_activity = [ 'subproject', 'activity', 'indicator', 'training', 'municipality', 'district', 'barangay', 'agency', 'mode', 'sector', 'person', 'resource', 'remarks', 'mov' ]; $search_conditions_activity = []; foreach ($searchable_columns_activity as $column) { $search_conditions_activity[] = "`" . $column . "` LIKE ?"; $query_params_activity[] = $like_term_activity; $query_param_types_activity .= 's'; } $where_clauses_activity[] = "(" . implode(" OR ", $search_conditions_activity) . ")"; }
foreach ($filter_columns_activity as $column) {
    $filter_value_activity = isset($_GET[$column]) ? trim($_GET[$column]) : '';
    if (!empty($filter_value_activity)) {
        if ($column === 'year') {
            $where_clauses_activity[] = "YEAR(`start`) = ?";
        } else {
            $where_clauses_activity[] = "`$column` = ?";
        }
        $query_params_activity[] = $filter_value_activity;
        $query_param_types_activity .= 's';
    }
}
$sql_where_clause_activity = !empty($where_clauses_activity) ? " WHERE " . implode(" AND ", $where_clauses_activity) : "";
$results_per_page_options = [5, 10, 20, 50, 100]; $default_results_per_page = 10; $current_page_activity = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1; $results_per_page_activity = isset($_GET['limit']) ? (int)$_GET['limit'] : $default_results_per_page; if (!in_array($results_per_page_activity, $results_per_page_options)) { $results_per_page_activity = $default_results_per_page; }
// Update URL params to include report year
$url_params_array_activity = ['tab' => 'activities'];
if (!empty($search_term_activity)) $url_params_array_activity['search'] = $search_term_activity;
if ($results_per_page_activity !== $default_results_per_page) $url_params_array_activity['limit'] = $results_per_page_activity;
foreach ($filter_columns_activity as $column) { if (isset($_GET[$column]) && trim($_GET[$column]) !== '') { $url_params_array_activity[$column] = trim($_GET[$column]); } }
$url_params_array_activity['p_search'] = $_GET['p_search'] ?? '';
$url_params_array_activity['p_limit'] = $_GET['p_limit'] ?? $default_results_per_page;
$url_params_array_activity['p_page'] = $_GET['p_page'] ?? 1;
$url_params_array_activity['year'] = $selected_report_year; // Add report year
$url_params_activity = '&' . http_build_query(array_filter($url_params_array_activity));

$total_activities = 0; $total_pages_activity = 0; $activities_result = false; $activities_fetch_error = null; $offset_activity = 0;
if (!$db_connection_error) { $total_activities_query = "SELECT COUNT(*) as total FROM tblactivity" . $sql_where_clause_activity; $total_activities = fetchStatValue($conn, $total_activities_query, $query_params_activity, $query_param_types_activity);
    if ($total_activities > 0) { $total_pages_activity = ceil($total_activities / $results_per_page_activity); if ($current_page_activity > $total_pages_activity) $current_page_activity = $total_pages_activity; if ($current_page_activity < 1) $current_page_activity = 1; $offset_activity = ($current_page_activity - 1) * $results_per_page_activity; $activities_sql = "SELECT id, start, end, project, subproject, activity, indicator, training, municipality, district, barangay, agency, mode, sector, person, resource, participants, completers, male, female, approved, mov, remarks FROM tblactivity" . $sql_where_clause_activity . " ORDER BY start DESC, id DESC LIMIT ? OFFSET ?"; $all_bind_params_activity = [...$query_params_activity, $results_per_page_activity, $offset_activity]; $combined_param_types_activity = $query_param_types_activity . 'ii'; $stmt_activities = mysqli_prepare($conn, $activities_sql);
        if ($stmt_activities) { if (count($all_bind_params_activity) !== strlen($combined_param_types_activity)) { $activities_fetch_error = "Error setting up activity data filter."; } else { mysqli_stmt_bind_param($stmt_activities, $combined_param_types_activity, ...$all_bind_params_activity); if (!mysqli_stmt_execute($stmt_activities)) { $activities_fetch_error = "Error fetching activity data."; } else { $activities_result = mysqli_stmt_get_result($stmt_activities); } } mysqli_stmt_close($stmt_activities); }
        else { $activities_fetch_error = "Error preparing to fetch activities."; }
    } else { $total_pages_activity = 1; if (empty($activities_fetch_error)) { if (!empty($search_term_activity) || count(array_filter(array_intersect_key($_GET, array_flip($filter_columns_activity)))) > 0 ) { $activities_fetch_error = "No ILCDB activities found matching the search/filter criteria."; } else { $activities_fetch_error = "No ILCDB activities found."; } } } // <-- Changed Text
} else { $activities_fetch_error = "Cannot fetch activities due to database connection error."; }
$table_colspan_activity = 24;

// === PARTICIPANTS TAB DATA ===
$participant_stats = []; $participant_stats_data = []; $participant_stats_data_current_month = []; $participant_stats_data_previous = []; $participant_stat_queries_ok = true;
if (!$db_connection_error) { /* Fetch participant stats */
    $total_participants_count_sql = "SELECT COUNT(*) AS total FROM tblparticipant" . $project_filter_where; $participant_stats_data['total'] = fetchStatValue($conn, $total_participants_count_sql, $params_project, $types_s_project); $male_participants_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Male'" . $project_filter_and; $participant_stats_data['male'] = fetchStatValue($conn, $male_participants_sql, $params_project, $types_s_project); $female_participants_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Female'" . $project_filter_and; $participant_stats_data['female'] = fetchStatValue($conn, $female_participants_sql, $params_project, $types_s_project); $unique_agencies_sql = "SELECT COUNT(DISTINCT agency) AS total FROM tblparticipant WHERE agency IS NOT NULL AND agency != ''" . $project_filter_and; $participant_stats_data['agencies'] = fetchStatValue($conn, $unique_agencies_sql, $params_project, $types_s_project); $face_to_face_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Face-to-Face'" . $project_filter_and; $participant_stats_data['face_to_face'] = fetchStatValue($conn, $face_to_face_sql, $params_project, $types_s_project); $virtual_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Virtual'" . $project_filter_and; $participant_stats_data['virtual'] = fetchStatValue($conn, $virtual_sql, $params_project, $types_s_project); $training_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Training'" . $project_filter_and; $participant_stats_data['training'] = fetchStatValue($conn, $training_sql, $params_project, $types_s_project); $awareness_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Awareness'" . $project_filter_and; $participant_stats_data['awareness'] = fetchStatValue($conn, $awareness_sql, $params_project, $types_s_project);
    $current_month_participants_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['total'] = fetchStatValue($conn, $current_month_participants_sql, $params_current_project, $types_ss_project); $current_month_male_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Male' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['male'] = fetchStatValue($conn, $current_month_male_sql, $params_current_project, $types_ss_project); $current_month_female_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Female' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['female'] = fetchStatValue($conn, $current_month_female_sql, $params_current_project, $types_ss_project); $current_month_agencies_sql = "SELECT COUNT(DISTINCT agency) AS total FROM tblparticipant WHERE agency IS NOT NULL AND agency != '' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['agencies'] = fetchStatValue($conn, $current_month_agencies_sql, $params_current_project, $types_ss_project); $current_month_face_to_face_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Face-to-Face' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['face_to_face'] = fetchStatValue($conn, $current_month_face_to_face_sql, $params_current_project, $types_ss_project); $current_month_virtual_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Virtual' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['virtual'] = fetchStatValue($conn, $current_month_virtual_sql, $params_current_project, $types_ss_project); $current_month_training_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Training' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['training'] = fetchStatValue($conn, $current_month_training_sql, $params_current_project, $types_ss_project); $current_month_awareness_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Awareness' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_current_month['awareness'] = fetchStatValue($conn, $current_month_awareness_sql, $params_current_project, $types_ss_project);
    $prev_month_participants_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['total'] = fetchStatValue($conn, $prev_month_participants_sql, $params_previous_project, $types_ss_project); $prev_month_male_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Male' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['male'] = fetchStatValue($conn, $prev_month_male_sql, $params_previous_project, $types_ss_project); $prev_month_female_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE sex = 'Female' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['female'] = fetchStatValue($conn, $prev_month_female_sql, $params_previous_project, $types_ss_project); $prev_month_agencies_sql = "SELECT COUNT(DISTINCT agency) AS total FROM tblparticipant WHERE agency IS NOT NULL AND agency != '' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['agencies'] = fetchStatValue($conn, $prev_month_agencies_sql, $params_previous_project, $types_ss_project); $prev_month_face_to_face_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Face-to-Face' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['face_to_face'] = fetchStatValue($conn, $prev_month_face_to_face_sql, $params_previous_project, $types_ss_project); $prev_month_virtual_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE mode = 'Virtual' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['virtual'] = fetchStatValue($conn, $prev_month_virtual_sql, $params_previous_project, $types_ss_project); $prev_month_training_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Training' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['training'] = fetchStatValue($conn, $prev_month_training_sql, $params_previous_project, $types_ss_project); $prev_month_awareness_sql = "SELECT COUNT(*) AS total FROM tblparticipant WHERE indicator = 'Awareness' AND start >= ? AND start <= ?" . $project_filter_and; $participant_stats_data_previous['awareness'] = fetchStatValue($conn, $prev_month_awareness_sql, $params_previous_project, $types_ss_project);
}
if ($participant_stat_queries_ok) {
     $change_total_participants = calculateChange($participant_stats_data_current_month['total'], $participant_stats_data_previous['total']);
     $participant_stats[] = [
         'key' => 'p_total', 'icon' => 'fas fa-users', 'title' => 'Total Participants',
         'value' => number_format($participant_stats_data['total']),
         'current_month_value' => number_format($participant_stats_data_current_month['total']),
         'prev_month_value' => number_format($participant_stats_data_previous['total']),
         'insight' => $change_total_participants
     ];
     $change_male_participants = calculateChange($participant_stats_data_current_month['male'], $participant_stats_data_previous['male']);
     $participant_stats[] = [
         'key' => 'p_male', 'icon' => 'fas fa-male', 'title' => 'Male Participants',
         'value' => number_format($participant_stats_data['male']),
         'current_month_value' => number_format($participant_stats_data_current_month['male']),
         'prev_month_value' => number_format($participant_stats_data_previous['male']),
         'insight' => $change_male_participants
     ];
     $change_female_participants = calculateChange($participant_stats_data_current_month['female'], $participant_stats_data_previous['female']);
     $participant_stats[] = [
         'key' => 'p_female', 'icon' => 'fas fa-female', 'title' => 'Female Participants',
         'value' => number_format($participant_stats_data['female']),
         'current_month_value' => number_format($participant_stats_data_current_month['female']),
         'prev_month_value' => number_format($participant_stats_data_previous['female']),
         'insight' => $change_female_participants
     ];
     $change_unique_agencies = calculateChange($participant_stats_data_current_month['agencies'], $participant_stats_data_previous['agencies']);
     $participant_stats[] = [
         'key' => 'p_agencies', 'icon' => 'fas fa-building', 'title' => 'Unique Agencies',
         'value' => number_format($participant_stats_data['agencies']),
         'current_month_value' => number_format($participant_stats_data_current_month['agencies']),
         'prev_month_value' => number_format($participant_stats_data_previous['agencies']),
         'insight' => $change_unique_agencies
     ];
     $change_face_to_face = calculateChange($participant_stats_data_current_month['face_to_face'], $participant_stats_data_previous['face_to_face']);
     $participant_stats[] = [
         'key' => 'p_f2f', 'icon' => 'fas fa-user-friends', 'title' => 'Face-to-Face Participants',
         'value' => number_format($participant_stats_data['face_to_face']),
         'current_month_value' => number_format($participant_stats_data_current_month['face_to_face']),
         'prev_month_value' => number_format($participant_stats_data_previous['face_to_face']),
         'insight' => $change_face_to_face
     ];
     $change_virtual = calculateChange($participant_stats_data_current_month['virtual'], $participant_stats_data_previous['virtual']);
     $participant_stats[] = [
         'key' => 'p_virtual', 'icon' => 'fas fa-laptop', 'title' => 'Virtual Participants',
         'value' => number_format($participant_stats_data['virtual']),
         'current_month_value' => number_format($participant_stats_data_current_month['virtual']),
         'prev_month_value' => number_format($participant_stats_data_previous['virtual']),
         'insight' => $change_virtual
     ];
     $change_training = calculateChange($participant_stats_data_current_month['training'], $participant_stats_data_previous['training']);
     $participant_stats[] = [
         'key' => 'p_training', 'icon' => 'fas fa-chalkboard', 'title' => 'Training Participants',
         'value' => number_format($participant_stats_data['training']),
         'current_month_value' => number_format($participant_stats_data_current_month['training']),
         'prev_month_value' => number_format($participant_stats_data_previous['training']),
         'insight' => $change_training
     ];
     $change_awareness = calculateChange($participant_stats_data_current_month['awareness'], $participant_stats_data_previous['awareness']);
     $participant_stats[] = [
         'key' => 'p_awareness', 'icon' => 'fas fa-bullhorn', 'title' => 'Awareness Participants',
         'value' => number_format($participant_stats_data['awareness']),
         'current_month_value' => number_format($participant_stats_data_current_month['awareness']),
         'prev_month_value' => number_format($participant_stats_data_previous['awareness']),
         'insight' => $change_awareness
     ];
} else {
     $participant_stats[] = [
         'key' => 'p_error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Participant Stats Error',
         'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Could not load participant stats.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
     ];
 }

// *** START PARTICIPANT TABLE LOGIC ***
$search_term_participant = isset($_GET['p_search']) ? trim($_GET['p_search']) : '';
$current_page_participant = isset($_GET['p_page']) ? max(1, (int)$_GET['p_page']) : 1;
$results_per_page_participant = isset($_GET['p_limit']) ? (int)$_GET['p_limit'] : $default_results_per_page;
if (!in_array($results_per_page_participant, $results_per_page_options)) {
    $results_per_page_participant = $default_results_per_page;
}
$where_clauses_participant = ["project = ?"];
$query_params_participant = [$project_filter_value];
$query_param_types_participant = 's';
if (!empty($search_term_participant)) {
    $like_term_participant = '%' . $search_term_participant . '%';
    $searchable_columns_participant = [ 'start', 'end', 'activity', 'indicator', 'fullname', 'sex', 'contact', 'email', 'mode', 'agency', 'sector', 'person', 'remarks' ];
    $search_conditions_participant = [];
    foreach ($searchable_columns_participant as $column) { $search_conditions_participant[] = "`" . $column . "` LIKE ?"; $query_params_participant[] = $like_term_participant; $query_param_types_participant .= 's'; }
    $where_clauses_participant[] = "(" . implode(" OR ", $search_conditions_participant) . ")";
}
$filter_columns_participant = ['year', 'indicator', 'mode', 'agency'];
foreach ($filter_columns_participant as $column) {
    $param_name = 'p_' . $column;
    $filter_value_participant = isset($_GET[$param_name]) ? trim($_GET[$param_name]) : '';
    if (!empty($filter_value_participant)) {
        if ($column === 'year') {
            $where_clauses_participant[] = "YEAR(`start`) = ?";
        } else {
            $where_clauses_participant[] = "`$column` = ?";
        }
        $query_params_participant[] = $filter_value_participant;
        $query_param_types_participant .= 's';
    }
}
$sql_where_clause_participant = !empty($where_clauses_participant) ? " WHERE " . implode(" AND ", $where_clauses_participant) : "";
// Update participant URL params to include report year
$url_params_array_participant = ['tab' => 'participants'];
if (!empty($search_term_participant)) $url_params_array_participant['p_search'] = $search_term_participant;
if ($results_per_page_participant !== $default_results_per_page) $url_params_array_participant['p_limit'] = $results_per_page_participant;
foreach ($filter_columns_participant as $column) {
    $param_name = 'p_' . $column;
    if (isset($_GET[$param_name]) && trim($_GET[$param_name]) !== '') { $url_params_array_participant[$param_name] = trim($_GET[$param_name]); }
}
$url_params_array_participant['search'] = $_GET['search'] ?? '';
$url_params_array_participant['limit'] = $_GET['limit'] ?? $default_results_per_page;
$url_params_array_participant['page'] = $_GET['page'] ?? 1;
$url_params_array_participant['year'] = $selected_report_year; // Add report year
foreach ($filter_columns_activity as $column) { if (isset($_GET[$column]) && trim($_GET[$column]) !== '') { $url_params_array_participant[$column] = trim($_GET[$column]); } }
$url_params_participant = '&' . http_build_query(array_filter($url_params_array_participant));

$total_participants = 0; $total_pages_participant = 0; $participants_result = false; $participants_fetch_error = null; $offset_participant = 0;
if (!$db_connection_error) {
    $total_participants_query = "SELECT COUNT(*) as total FROM tblparticipant" . $sql_where_clause_participant;
    $total_participants = fetchStatValue($conn, $total_participants_query, $query_params_participant, $query_param_types_participant);
    if ($total_participants > 0) {
        $total_pages_participant = ceil($total_participants / $results_per_page_participant);
        if ($current_page_participant > $total_pages_participant) $current_page_participant = $total_pages_participant;
        if ($current_page_participant < 1) $current_page_participant = 1;
        $offset_participant = ($current_page_participant - 1) * $results_per_page_participant;
        $participants_sql = "SELECT `id`, `start`, `end`, `activity`, `indicator`, `fullname`, `sex`, `contact`, `email`, `mode`, `agency`, `sector`, `project`, `person`, `remarks` FROM tblparticipant" . $sql_where_clause_participant . " ORDER BY start DESC, id DESC LIMIT ? OFFSET ?";
        $all_bind_params_participant = [...$query_params_participant, $results_per_page_participant, $offset_participant];
        $combined_param_types_participant = $query_param_types_participant . 'ii';
        $stmt_participants = mysqli_prepare($conn, $participants_sql);
        if ($stmt_participants) {
            if (count($all_bind_params_participant) !== strlen($combined_param_types_participant)) { $participants_fetch_error = "Error setting up participant data filter: Param/Type count mismatch."; error_log("Participant Fetch Mismatch: P".count($all_bind_params_participant)."!=T".strlen($combined_param_types_participant)."|SQL:".$participants_sql); }
            else { mysqli_stmt_bind_param($stmt_participants, $combined_param_types_participant, ...$all_bind_params_participant); if (!mysqli_stmt_execute($stmt_participants)) { $participants_fetch_error = "Error fetching participant data: " . mysqli_stmt_error($stmt_participants); } else { $participants_result = mysqli_stmt_get_result($stmt_participants); } }
            mysqli_stmt_close($stmt_participants);
        } else { $participants_fetch_error = "Error preparing to fetch participants: " . mysqli_error($conn); }
    } else { $total_pages_participant = 1; if (empty($participants_fetch_error)) { if (!empty($search_term_participant) || !empty(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'p_'.$c, $filter_columns_participant))))) ) { $participants_fetch_error = "No ILCDB participants found matching the search/filter criteria."; } else { $participants_fetch_error = "No ILCDB participants found."; } } } // <-- Changed Text
} else { $participants_fetch_error = "Cannot fetch participants due to database connection error."; }
$table_colspan_participant = 16;
// *** END PARTICIPANT TABLE LOGIC ***

// === TECH4ED DTC TAB DATA ===
// --- Tech4ED Statistics ---
$tech4ed_stats = []; $tech4ed_stats_data = []; $tech4ed_stats_data_current_month = []; $tech4ed_stats_data_previous = []; $tech4ed_stat_queries_ok = true;

if (!$db_connection_error) {
    // Total Tech4ED DTCs
    $total_tech4ed_count_sql = "SELECT COUNT(*) AS total FROM tbltech4ed";
    $tech4ed_stats_data['total'] = fetchStatValue($conn, $total_tech4ed_count_sql);

    // Count by category
    // LGU
    $lgu_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'LGU'";
    $tech4ed_stats_data['lgu'] = fetchStatValue($conn, $lgu_tech4ed_sql);

    // School
    $school_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'School'";
    $tech4ed_stats_data['school'] = fetchStatValue($conn, $school_tech4ed_sql);

    // NGA
    $nga_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'NGA'";
    $tech4ed_stats_data['nga'] = fetchStatValue($conn, $nga_tech4ed_sql);

    // Private
    $private_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'Private'";
    $tech4ed_stats_data['private'] = fetchStatValue($conn, $private_tech4ed_sql);

    // RIS
    $ris_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'RIS'";
    $tech4ed_stats_data['ris'] = fetchStatValue($conn, $ris_tech4ed_sql);

    // DICT Provincial Training Center
    $provincial_center_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE category = 'DICT Provincial Training Center'";
    $tech4ed_stats_data['provincial_center'] = fetchStatValue($conn, $provincial_center_sql);

    // Operational Status
    $operational_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE operation = 'Operational'";
    $tech4ed_stats_data['operational'] = fetchStatValue($conn, $operational_sql);

    $non_operational_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE operation = 'Non-Operational'";
    $tech4ed_stats_data['non_operational'] = fetchStatValue($conn, $non_operational_sql);

    // Current month stats
    $current_month_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE registration >= ? AND registration <= ?";
    $tech4ed_stats_data_current_month['total'] = fetchStatValue($conn, $current_month_tech4ed_sql, [$current_month_start, $current_month_end], 'ss');

    // Previous month stats
    $prev_month_tech4ed_sql = "SELECT COUNT(*) AS total FROM tbltech4ed WHERE registration >= ? AND registration <= ?";
    $tech4ed_stats_data_previous['total'] = fetchStatValue($conn, $prev_month_tech4ed_sql, [$prev_month_start, $prev_month_end], 'ss');
}

if ($tech4ed_stat_queries_ok) {
    // We're not showing the total Tech4ED DTCs as requested

    // LGU Category
    $tech4ed_stats[] = [
        'key' => 't_lgu', 'icon' => 'fas fa-city', 'title' => 'LGU Centers',
        'value' => number_format($tech4ed_stats_data['lgu']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DTCs under Local Government Units', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // School Category
    $tech4ed_stats[] = [
        'key' => 't_school', 'icon' => 'fas fa-school', 'title' => 'School Centers',
        'value' => number_format($tech4ed_stats_data['school']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DTCs located in schools', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // NGA Category
    $tech4ed_stats[] = [
        'key' => 't_nga', 'icon' => 'fas fa-landmark', 'title' => 'NGA Centers',
        'value' => number_format($tech4ed_stats_data['nga']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DTCs under National Government Agencies', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // Private Category
    $tech4ed_stats[] = [
        'key' => 't_private', 'icon' => 'fas fa-store', 'title' => 'Private Centers',
        'value' => number_format($tech4ed_stats_data['private']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DTCs under private organizations', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // RIS Category
    $tech4ed_stats[] = [
        'key' => 't_ris', 'icon' => 'fas fa-broadcast-tower', 'title' => 'RIS Centers',
        'value' => number_format($tech4ed_stats_data['ris']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DTCs under Rural Impact Sourcing', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // Provincial Training Center
    $tech4ed_stats[] = [
        'key' => 't_provincial', 'icon' => 'fas fa-university', 'title' => 'Provincial Centers',
        'value' => number_format($tech4ed_stats_data['provincial_center']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'DICT Provincial Training Centers', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // Operational Status
    $tech4ed_stats[] = [
        'key' => 't_operational', 'icon' => 'fas fa-check-circle', 'title' => 'Operational Centers',
        'value' => number_format($tech4ed_stats_data['operational']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Currently operational DTCs', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];

    // Non-Operational Status
    $tech4ed_stats[] = [
        'key' => 't_non_operational', 'icon' => 'fas fa-times-circle', 'title' => 'Non-Operational Centers',
        'value' => number_format($tech4ed_stats_data['non_operational']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Currently non-operational DTCs', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-neutral']
    ];
} else {
    $tech4ed_stats[] = [
        'key' => 't_error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Tech4ED Stats Error',
        'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Could not load Tech4ED statistics.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
    ];
}

$filter_columns_tech4ed = ['district', 'municipality', 'category', 'status', 'network', 'connectivity']; // Filter columns for Tech4ED (removed region and province)
$search_term_tech4ed = isset($_GET['t_search']) ? trim($_GET['t_search']) : '';
$where_clauses_tech4ed = []; // No initial project filter for Tech4ED
$query_params_tech4ed = [];
$query_param_types_tech4ed = '';

if (!empty($search_term_tech4ed)) {
    $like_term_tech4ed = '%' . $search_term_tech4ed . '%';
    // Define searchable columns for tbltech4ed - adjust as needed
    $searchable_columns_tech4ed = [
        'region', 'province', 'district', 'municipality', 'barangay', 'location', 'cname', 'host', 'category',
        'cmanager', 'amanager', 'status', 'network', 'connectivity', 'partner', 'identifier'
    ];
    $search_conditions_tech4ed = [];
    foreach ($searchable_columns_tech4ed as $column) {
        $search_conditions_tech4ed[] = "`" . $column . "` LIKE ?";
        $query_params_tech4ed[] = $like_term_tech4ed;
        $query_param_types_tech4ed .= 's';
    }
    $where_clauses_tech4ed[] = "(" . implode(" OR ", $search_conditions_tech4ed) . ")";
}

foreach ($filter_columns_tech4ed as $column) {
    $filter_value_tech4ed = isset($_GET['t_' . $column]) ? trim($_GET['t_' . $column]) : '';
    if (!empty($filter_value_tech4ed)) {
        $where_clauses_tech4ed[] = "`$column` = ?";
        $query_params_tech4ed[] = $filter_value_tech4ed;
        $query_param_types_tech4ed .= 's';
    }
}

$sql_where_clause_tech4ed = !empty($where_clauses_tech4ed) ? " WHERE " . implode(" AND ", $where_clauses_tech4ed) : "";

$current_page_tech4ed = isset($_GET['t_page']) ? max(1, (int)$_GET['t_page']) : 1;
$results_per_page_tech4ed = isset($_GET['t_limit']) ? (int)$_GET['t_limit'] : $default_results_per_page;
if (!in_array($results_per_page_tech4ed, $results_per_page_options)) {
    $results_per_page_tech4ed = $default_results_per_page;
}

// Update URL params for Tech4ED tab
$url_params_array_tech4ed = ['tab' => 'tech4ed'];
if (!empty($search_term_tech4ed)) $url_params_array_tech4ed['t_search'] = $search_term_tech4ed;
if ($results_per_page_tech4ed !== $default_results_per_page) $url_params_array_tech4ed['t_limit'] = $results_per_page_tech4ed;
foreach ($filter_columns_tech4ed as $column) {
    if (isset($_GET['t_' . $column]) && trim($_GET['t_' . $column]) !== '') {
        $url_params_array_tech4ed['t_' . $column] = trim($_GET['t_' . $column]);
    }
}
// Carry over params from other tabs for consistent URL state
$url_params_array_tech4ed['search'] = $_GET['search'] ?? '';
$url_params_array_tech4ed['limit'] = $_GET['limit'] ?? $default_results_per_page;
$url_params_array_tech4ed['page'] = $_GET['page'] ?? 1;
$url_params_array_tech4ed['p_search'] = $_GET['p_search'] ?? '';
$url_params_array_tech4ed['p_limit'] = $_GET['p_limit'] ?? $default_results_per_page;
$url_params_array_tech4ed['p_page'] = $_GET['p_page'] ?? 1;
foreach ($filter_columns_activity as $column) { if (isset($_GET[$column]) && trim($_GET[$column]) !== '') { $url_params_array_tech4ed[$column] = trim($_GET[$column]); } }
foreach ($filter_columns_participant as $column) { if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== '') { $url_params_array_tech4ed['p_'.$column] = trim($_GET['p_'.$column]); } }
$url_params_array_tech4ed['year'] = $selected_report_year; // Report year
$url_params_tech4ed = '&' . http_build_query(array_filter($url_params_array_tech4ed));

$total_tech4ed = 0;
$total_pages_tech4ed = 0;
$tech4ed_result = false;
$tech4ed_fetch_error = null;
$offset_tech4ed = 0;

if (!$db_connection_error) {
    $total_tech4ed_query = "SELECT COUNT(*) as total FROM tbltech4ed" . $sql_where_clause_tech4ed;
    // Use fetchStatValue for count, ensuring parameters are passed correctly if where clause exists
    if (!empty($query_params_tech4ed)) {
        $total_tech4ed = fetchStatValue($conn, $total_tech4ed_query, $query_params_tech4ed, $query_param_types_tech4ed);
    } else {
        $total_tech4ed = fetchStatValue($conn, $total_tech4ed_query); // No params if no where clause
    }

    if ($total_tech4ed > 0) {
        $total_pages_tech4ed = ceil($total_tech4ed / $results_per_page_tech4ed);
        if ($current_page_tech4ed > $total_pages_tech4ed) $current_page_tech4ed = $total_pages_tech4ed;
        if ($current_page_tech4ed < 1) $current_page_tech4ed = 1;
        $offset_tech4ed = ($current_page_tech4ed - 1) * $results_per_page_tech4ed;

        $tech4ed_sql = "SELECT `id`, `region`, `province`, `district`, `municipality`, `barangay`, `street`, `location`, `cname`, `host`, `category`, `longitude`, `latitude`, `cmanager`, `cemail`, `cmobile`, `clandline`, `cgender`, `amanager`, `aemail`, `amobile`, `alandline`, `agender`, `launch`, `registration`, `operation`, `visited`, `desktop`, `laptop`, `printer`, `scanner`, `status`, `network`, `connectivity`, `speed`, `cmtmale`, `cmtfemale`, `straining`, `etraining`, `signing`, `partner`, `expiration`, `donation`, `datedonation`, `tcms`, `key_one`, `identifier` FROM tbltech4ed" . $sql_where_clause_tech4ed . " ORDER BY municipality ASC, id DESC LIMIT ? OFFSET ?";

        $all_bind_params_tech4ed = [...$query_params_tech4ed, $results_per_page_tech4ed, $offset_tech4ed];
        $combined_param_types_tech4ed = $query_param_types_tech4ed . 'ii';

        $stmt_tech4ed = mysqli_prepare($conn, $tech4ed_sql);
        if ($stmt_tech4ed) {
            if (count($all_bind_params_tech4ed) !== strlen($combined_param_types_tech4ed)) {
                $tech4ed_fetch_error = "Error setting up Tech4ED DTC data filter: Parameter/Type count mismatch.";
                error_log("Tech4ED Fetch Mismatch: P".count($all_bind_params_tech4ed)."!=T".strlen($combined_param_types_tech4ed)."|SQL:" . $tech4ed_sql);
            } else {
                mysqli_stmt_bind_param($stmt_tech4ed, $combined_param_types_tech4ed, ...$all_bind_params_tech4ed);
                if (!mysqli_stmt_execute($stmt_tech4ed)) {
                    $tech4ed_fetch_error = "Error fetching Tech4ED DTC data: " . mysqli_stmt_error($stmt_tech4ed);
                } else {
                    $tech4ed_result = mysqli_stmt_get_result($stmt_tech4ed);
                }
            }
            mysqli_stmt_close($stmt_tech4ed);
        } else {
            $tech4ed_fetch_error = "Error preparing to fetch Tech4ED DTCs: " . mysqli_error($conn);
        }
    } else {
        $total_pages_tech4ed = 1; // Still show 1 page for pagination controls consistency
        if (empty($tech4ed_fetch_error)) { // Only set message if no other error occurred
            if (!empty($search_term_tech4ed) || count(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 't_'.$c, $filter_columns_tech4ed))))) > 0 ) {
                 $tech4ed_fetch_error = "No Tech4ED DTCs found matching the search/filter criteria.";
            } else {
                 $tech4ed_fetch_error = "No Tech4ED DTCs found.";
            }
        }
    }
} else {
    $tech4ed_fetch_error = "Cannot fetch Tech4ED DTCs due to database connection error.";
}
$table_colspan_tech4ed = 48; // 46 columns + 1 for checkbox + 1 for rownum


// --- Helper Date Functions ---
function formatDateForDisplay($date_string) { if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('M d, Y', $timestamp) : ''; }
function formatDateForInput($date_string) { if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('Y-m-d', $timestamp) : ''; }

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Wifi for All Dashboard - Activity Monitoring</title> <!-- <-- Changed Title -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/filter-dropdown.css"> <!-- Filter dropdown styles -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
    <!-- ADD Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* ----- Navigation Tabs Container ----- */
        .nav-tabs-container { margin-bottom: 20px; }
        .nav-tabs { display: flex; border-bottom: 1px solid #ddd; margin-bottom: 0; background-color: #f8f9fa; border-radius: 4px 4px 0 0; justify-content: space-between; flex-wrap: wrap; /* Allow wrapping on small screens */ }
        .nav-tabs-left { display: flex; flex-grow: 1; /* Allow left side to take space */ }
        .nav-tabs-right { display: flex; align-items: center; padding-right: 10px; gap: 8px; flex-shrink: 0; /* Prevent shrinking too much */ padding-top: 5px; padding-bottom: 5px; /* Add padding for wrapped items */ }
        .nav-tab { padding: 12px 20px; color: #495057; text-decoration: none; font-weight: 500; border-bottom: 3px solid transparent; transition: all 0.2s ease; cursor: pointer; white-space: nowrap; }
        .nav-tab:hover { color: var(--primary-color); background-color: rgba(0, 123, 255, 0.05); }
        .nav-tab.active { color: var(--primary-color); border-bottom: 3px solid var(--primary-color); }
        .tab-content { display: none; padding: 0; }
        .tab-content.active { display: block; }
        .btn-sm { padding: 6px 12px; font-size: 13px; }
        .nav-tabs-right .btn-secondary { background-color: transparent; border-color: #ddd; color: #495057; }
        .nav-tabs-right .btn-secondary:hover { background-color: rgba(0, 123, 255, 0.05); color: var(--primary-color); }
        .nav-tabs-right .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: white; }
        .nav-tabs-right .btn-primary:hover { background-color: #5a4bd3; border-color: #5a4bd3; }
        .content-placeholder { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 60px 20px; background-color: #f8f9fa; border-radius: 4px; color: #6c757d; text-align: center; margin-bottom: 20px; }
        .content-placeholder i { margin-bottom: 15px; color: #adb5bd; }
        .content-placeholder h3 { margin-bottom: 10px; font-weight: 500; }

        /* Style for the report year filter */
        #reportYearFilter {
            padding: 5px 8px; /* Slightly smaller padding */
            font-size: 13px;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            margin-left: 5px; /* Space from Manage Targets button */
            height: 31px; /* Match btn-sm height approx */
            vertical-align: middle; /* Align with buttons */
        }
        #reportYearFilter:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2);
        }


        /* ----- Enhanced View Files Modal Styles ----- */
        .modal.large .modal-content { max-width: 800px; }
        #viewFilesModal .modal-body { padding-top: 15px; } /* Adjusted padding */
        .view-files-header { display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; margin-bottom: 15px; border-bottom: 1px solid var(--border-color); } /* Moved styles here */
        .view-files-header p { margin: 0; font-weight: 500; color: var(--text-dark); }
        .view-files-controls { display: flex; align-items: center; gap: 15px; }
        .view-files-controls .checkbox-label { display: flex; align-items: center; cursor: pointer; font-size: 14px; margin-bottom: 0; }
        .view-files-controls input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); }
        .view-files-controls .btn-delete { padding: 5px 10px; font-size: 13px; }
        #fileListContainer.grouped { max-height: 55vh; overflow-y: auto; padding: 0 5px 5px 5px; /* Reduced padding slightly */ }
        .file-group { margin-bottom: 15px; border: 1px solid var(--border-color); border-radius: 4px; background-color: #fff; transition: background-color 0.2s ease; }
        .file-group:last-child { margin-bottom: 5px; }
        .file-group-header { display: flex; align-items: center; padding: 10px 15px; background-color: #f8f9fa; border-bottom: 1px solid var(--border-color); cursor: pointer; position: sticky; top: 0; z-index: 1; }
        .file-group.collapsed + .file-group .file-group-header { border-top: none; }
        .file-group-header .toggle-group { background: none; border: none; padding: 0 10px 0 0; margin: 0; cursor: pointer; color: var(--primary-color); font-size: 14px; transition: transform 0.2s ease; line-height: 1; }
        .file-group.collapsed .toggle-group i { transform: rotate(-90deg); }
        .file-group:not(.collapsed) .toggle-group i { transform: rotate(0deg); }
        .file-group-header .group-title { font-weight: 600; color: var(--text-dark); flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-right: 10px; }
        .file-group-header .badge { background-color: var(--primary-color); color: white; padding: 3px 8px; border-radius: 10px; font-size: 12px; font-weight: 500; flex-shrink: 0; }
        .file-group-content { padding: 0px 15px 5px 15px; display: block; transition: max-height 0.3s ease-out, opacity 0.3s ease-out; max-height: 1000px; overflow: hidden; }
        .file-group.collapsed .file-group-content { max-height: 0; opacity: 0; padding-top: 0; padding-bottom: 0; border-top: none; }
        .file-item { display: flex; align-items: center; padding: 8px 0; border-bottom: 1px dashed var(--border-light); gap: 10px; }
        .file-item:last-child { border-bottom: none; padding-bottom: 10px; }
        .file-group.collapsed .file-item { display: none; }
        .file-item input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); flex-shrink: 0; vertical-align: middle; margin-top: 0; }
        .file-item .file-icon { font-size: 1.4em; color: #6c757d; width: 25px; text-align: center; flex-shrink: 0; }
        .file-item .fa-file-pdf { color: #dc3545; } .file-item .fa-file-word { color: #0d6efd; } .file-item .fa-file-excel { color: #198754; } .file-item .fa-file-image { color: #ffc107; } .file-item .fa-file-archive { color: #6f42c1; }
        .file-item .file-details { flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; min-width: 0; }
        .file-item .file-name { font-weight: 500; color: var(--text-dark); white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; }
        .file-item .file-meta { font-size: 12px; color: var(--text-light); }
        .file-item .file-size { font-size: 13px; color: var(--text-medium); white-space: nowrap; flex-shrink: 0; min-width: 80px; text-align: right; }
        .file-item .btn-icon { background: none; border: none; color: var(--text-light); cursor: pointer; padding: 3px; font-size: 14px; flex-shrink: 0; margin-left: 5px; }
        .file-item .btn-icon:hover { color: var(--primary-color); }
        .file-item .btn-preview-file { color: var(--text-light); }
        .file-item .btn-preview-file:hover { color: var(--primary-color); }
        .file-item .btn-download-file { color: var(--text-light); }
        .file-item .btn-download-file:hover { color: var(--primary-color); }
        #viewFilesModal .loading-indicator { display: none; justify-content: center; align-items: center; padding: 30px; font-size: 16px; color: var(--text-light); }
        #viewFilesModal .loading-indicator.active { display: flex; }
        #viewFilesModal .empty-message, #viewFilesModal .error-message { text-align: center; padding: 30px; color: var(--text-light); display: none; }
        #viewFilesModal .error-message { color: var(--red-color); }
        #viewFilesSelectAll:indeterminate { accent-color: var(--primary-color); }
        .col-checkbox input[type="checkbox"] { vertical-align: middle; }

        /* --- Target Management Modal --- */
        #manageTargetsModal .modal-content { max-width: 950px; }
        #manageTargetsModal .modal-body { padding-top: 10px; }
        #existingTargetsTableContainer { max-height: 300px; overflow-y: auto; margin-bottom: 20px; border: 1px solid var(--border-color); }
        #existingTargetsTable { width: 100%; border-collapse: collapse; }
        #existingTargetsTable th, #existingTargetsTable td { padding: 8px 10px; font-size: 13px; text-align: left; border-bottom: 1px solid var(--border-light); }
        #existingTargetsTable th { font-weight: 600; color: var(--text-light); background-color: var(--bg-color); }
        #existingTargetsTable thead { position: sticky; top: 0; background-color: var(--bg-color); z-index: 1; }
        #existingTargetsTable .actions-col { width: 80px; text-align: center; }
        #existingTargetsTable .btn-icon { background: none; border: none; padding: 3px; font-size: 14px; cursor: pointer; color: var(--text-light); }
        #existingTargetsTable .btn-icon.edit-target:hover { color: var(--primary-color); }
        #existingTargetsTable .btn-icon.delete-target:hover { color: var(--red-color); }
        #addTargetFormContainer { margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color); }
        #addTargetFormContainer h4 { margin-bottom: 15px; font-weight: 600; color: var(--secondary-color); }
        #addTargetForm .form-grid { grid-template-columns: repeat(5, 1fr); gap: 15px; } /* 5 columns for better layout */
        #addTargetForm .category-field { grid-column: span 1; }
        #addTargetForm .subcategory-field { grid-column: span 2; }
        #addTargetForm .indicator-field { grid-column: span 2; }
        #addTargetForm .year-field { grid-column: span 1; }
        #addTargetForm .target-field { grid-column: span 1; }
        #addTargetForm .form-field label { margin-bottom: 3px; font-size: 12px; }
        #addTargetForm input[readonly] { background-color: #eee; cursor: not-allowed; }
        #addTargetForm .required { color: var(--red-color); margin-left: 2px;}
        #addTargetForm .form-field small { font-size: 11px; color: var(--text-light); margin-top: 2px; display: block;}
        .modal-footer.target-modal-footer { display: flex; justify-content: space-between; align-items: center; }
        .target-modal-footer .left-buttons { display: flex; gap: 10px; }
        .target-modal-footer .right-buttons { display: flex; gap: 10px; }

        /* Remove hover effect for Close Filters button */
        .filter-toggle-button.active:hover {
            background-color: var(--primary-color) !important;
            color: white !important;
            border-color: var(--primary-color) !important;
        }

        /* Make stat cards clickable with modern gradient border effect */
        .stat-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease, border 0.3s ease;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: var(--border-radius);
            padding: 1px; /* Thin border width */
            background: linear-gradient(
                135deg,
                rgba(106, 90, 224, 0) 0%,
                rgba(106, 90, 224, 0.4) 50%,
                rgba(106, 90, 224, 0) 100%
            );
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        /* Futuristic animated gradient border */
        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: var(--border-radius);
            padding: 1px;
            background: linear-gradient(
                90deg,
                rgba(106, 90, 224, 0) 0%,
                rgba(106, 90, 224, 0.2) 25%,
                rgba(106, 90, 224, 0.5) 50%,
                rgba(106, 90, 224, 0.2) 75%,
                rgba(106, 90, 224, 0) 100%
            );
            background-size: 200% 100%;
            background-position: 100% 0;
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: none;
        }

        @keyframes gradientMove {
            0% {
                background-position: 100% 0;
            }
            100% {
                background-position: 0% 0;
            }
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 12px rgba(106, 90, 224, 0.1);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover::after {
            opacity: 1;
            animation: gradientMove 1.5s linear infinite;
        }

        /* Statistics Breakdown Modal Styles (from freewifi4all.php) */
        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0;
        }

        .stats-tabs {
            display: flex;
        }

        .stats-tab-btn {
            padding: 8px 15px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.2s ease;
        }

        .stats-tab-btn:hover {
            color: var(--primary-color);
        }

        .stats-tab-btn.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .stats-view {
            margin-top: 15px;
        }

        /* Statistics Table Controls and Pagination Styles */
        .stats-table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 8px 12px;
        }

        .stats-table-length {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
        }

        .stats-table-length select {
            margin: 0 5px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            width: 50px;
            font-size: 13px;
            color: #333;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 6px center;
            background-size: 12px;
            padding-right: 20px;
        }

        .stats-table-search {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
            position: relative;
        }

        .stats-table-search input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            color: #333;
            background-color: white;
            width: 250px;
        }

        .stats-table-container {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
            border-top: 1px solid var(--border-light);
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .stats-table th,
        .stats-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table th {
            background-color: var(--bg-light);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .stats-table tr:hover {
            background-color: rgba(106, 90, 224, 0.05);
        }

        .stats-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 8px 12px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .stats-table-info {
            color: #666;
            font-size: 13px;
        }

        .stats-table-pages {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 6px;
            cursor: pointer;
            color: #6c757d;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
            height: 32px;
        }

        .pagination-btn:hover:not([disabled]) {
            background-color: #e9ecef;
            color: #495057;
            border-color: #dee2e6;
        }

        .pagination-btn[disabled] {
            opacity: 0.5;
            cursor: not-allowed;
            color: #6c757d;
            background-color: #fff;
        }

        .pagination-numbers {
            display: flex;
            margin: 0 4px;
            gap: 2px;
        }

        .page-number {
            padding: 0;
            border: 1px solid #dee2e6;
            background-color: #fff;
            color: #6c757d;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
            height: 32px;
            line-height: 1;
            cursor: pointer;
        }

        .page-number:hover:not(.disabled) {
            background-color: #e9ecef;
            color: #495057;
            border-color: #adb5bd;
        }

        .page-number.active {
            background-color: #6f42c1;
            color: white;
            border-color: #6f42c1;
            font-weight: 600;
        }

        .page-number.disabled {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
            cursor: default;
            opacity: 0.6;
        }

        /* Chart Summary and Chart Type Selector Styles (from freewifi4all.php) */
        .chart-summary {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-light);
            flex-wrap: wrap;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-right: 60px;
        }

        .summary-label {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .chart-type-selector {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }

        .chart-type-btn {
            margin-left: 0;
        }

        .chart-type-btn:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .chart-type-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .chart-container {
            height: 350px;
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius);
            padding: 15px;
            background-color: white;
        }

        /* --- Report/Graph Styles --- */
        #reportGraphContainer {
            display: flex;
            flex-wrap: wrap;
            gap: 20px; /* Gap between graphs */
            margin-top: 20px; /* Adjust as needed */
            padding: 15px; /* Add padding to the container if it's a card */
            background-color: transparent; /* Assuming graphs are inside a card */
            border: none; /* Assuming graphs are inside a card */
        }
        .graph-wrapper {
            background-color: var(--bg-light);
            padding: 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            /* box-shadow removed as requested */
            flex: 1 1 calc(50% - 20px); /* Two columns, accounting for gap */
            min-width: 300px; /* Minimum width before wrapping */
            display: flex;
            flex-direction: column;
            height: 350px; /* Fixed height for consistency */
        }
        .graph-wrapper h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 15px;
            text-align: center;
            white-space: normal; /* Allow title to wrap */
        }
        .graph-wrapper canvas {
            max-width: 100%;
            flex-grow: 1; /* Allow canvas to take remaining space */
            height: auto !important; /* Ensure responsive height within flex */
            min-height: 200px; /* Prevent collapsing too much */
        }

        /* Responsive adjustments for graphs */
        @media (max-width: 768px) {
            .graph-wrapper { flex-basis: 100%; height: 300px; } /* One column, maybe reduce height */
        }

        /* Modern search field with icon */
        .search-container {
            position: relative;
            width: 100%;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
            z-index: 2;
            pointer-events: none;
        }

        .search-input {
            padding-left: 35px !important;
            padding-right: 10px !important;
            transition: all 0.3s ease;
            width: 100%;
        }

    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification"><p class="notification-message" id="notificationMessage"></p></div>
    <!-- App Container -->
    <div class="app-container">
        <?php
        // Set current page name for sidebar highlighting
        $current_page_name = 'ilcdb'; // Set to ilcdb for correct sidebar highlighting

        // Include the sidebar
        include 'sidebar.php';
        ?>
        <!-- Main Content -->
        <main class="app-main">
             <div class="dashboard-header">
                 <div class="office-header">
                     <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                     <div class="office-info"> <h1>DICT SDN - ILCDB Activities</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div> <!-- <-- Changed Title -->
                 </div>
             </div>
             <?php if ($db_connection_error): ?> <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div> <?php endif; ?>

             <!-- NAV TABS CONTAINER -->
            <section class="nav-tabs-container">
                <div class="nav-tabs">
                    <div class="nav-tabs-left">
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'activities') ? 'active' : ''; ?>" data-tab="activities">Activities Conducted</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'participants') ? 'active' : ''; ?>" data-tab="participants">Activity Participants</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'tech4ed') ? 'active' : ''; ?>" data-tab="tech4ed">Tech4ED DTC</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'reports') ? 'active' : ''; ?>" data-tab="reports">Graph/Report</a>
                    </div>
                    <div class="nav-tabs-right">
                         <!-- Year Filter (Shown only on Reports Tab via JS) -->
                        <select id="reportYearFilter" title="Select Report Year" style="display: <?php echo ($active_tab === 'reports') ? 'inline-block' : 'none'; ?>;">
                            <?php foreach ($available_years as $year_option): ?>
                            <option value="<?php echo htmlspecialchars($year_option); ?>" <?php echo ($year_option == $selected_report_year) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($year_option); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>

                        <!-- Manage Targets button (Shown only on Reports Tab via JS) -->
                        <button id="manageTargetsButton" class="btn btn-secondary btn-sm" title="Manage Targets" style="display: <?php echo ($active_tab === 'reports') ? 'inline-flex' : 'none'; ?>;">
                            <i class="fas fa-bullseye"></i> Manage Targets
                        </button>

                        <!-- Import/Export buttons (Hidden on Reports Tab via JS) -->
                        <button id="importButton" class="btn btn-secondary btn-sm" title="Import data from CSV" style="display: <?php echo ($active_tab !== 'reports') ? 'inline-flex' : 'none'; ?>;"><i class="fas fa-file-import"></i> Import</button>
                        <button id="exportButton" class="btn btn-primary btn-sm" title="Export data" style="display: <?php echo ($active_tab !== 'reports') ? 'inline-flex' : 'none'; ?>;"><i class="fas fa-file-export"></i> Export</button>
                    </div>
                </div>
            </section> <!-- END Nav Tabs Container -->


            <!-- Activities Tab -->
            <div class="tab-content <?php echo ($active_tab === 'activities') ? 'active' : ''; ?>" id="activities-tab">
                <!-- Activity Stats -->
                <section class="stats-cards">
                    <?php if (!empty($stats)): foreach ($stats as $stat): ?>
                        <?php $insight = $stat['insight'] ?? []; ?>
                        <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                            <div class="card-header">
                                <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                    <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                    <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                        <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                        <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="stat-card">
                            <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                            <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                        </div>
                    <?php endif; ?>
                </section>
                <!-- Activities Table -->
                <section class="recent-activities card">
                     <div class="activities-header"> <h2><?php echo !empty($search_term_activity) || count(array_filter(array_intersect_key($_GET, array_flip($filter_columns_activity)))) > 0 ? 'Filtered Free Wifi for All Activities' : 'All Free Wifi for All Activities'; ?></h2> <button id="addActivityButton" class="btn btn-primary"> <i class="fas fa-plus"></i> Add Activity </button> </div> <!-- <-- Changed Title -->
                     <div class="table-controls">
                        <form action="ilcdb.php" method="get" id="resultsPerPageFormActivity" class="results-per-page-form"> <input type="hidden" name="tab" value="activities"> <?php if (!empty($search_term_activity)): ?> <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_term_activity); ?>"> <?php endif; ?> <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?> <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>"> <?php endif; endforeach; ?> <input type="hidden" name="page" value="1"> <!-- Carry over participant params --> <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>"> <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>"> <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>"> <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>"> <!-- Carry over year --> <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?> <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>"> <?php endif; endforeach; ?> <label for="resultsPerPageSelectActivity">Result per page:</label> <select name="limit" id="resultsPerPageSelectActivity" onchange="this.form.submit();"> <?php foreach($results_per_page_options as $option): ?> <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_activity) echo 'selected'; ?>><?php echo $option; ?></option> <?php endforeach; ?> </select> </form> <!-- <-- Changed Action -->
                        <div class="table-right-controls">
                            <form action="ilcdb.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="search" name="search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_activity); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="activities">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="limit" value="<?php echo $results_per_page_activity; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_activity as $column): ?>
                                        <?php if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                        </div>
                     </div>

                     <!-- Filter Dropdown Area -->
                     <div id="activityFilters" class="filter-dropdown-container" style="display: none;">
                         <form action="ilcdb.php" method="get" class="filter-form"> <!-- <-- Changed Action -->
                             <input type="hidden" name="tab" value="activities">
                             <!-- Carry over other params -->
                             <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_term_activity); ?>">
                             <input type="hidden" name="limit" value="<?php echo htmlspecialchars($results_per_page_activity); ?>">
                             <input type="hidden" name="page" value="1">
                             <!-- Carry over participant params -->
                             <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                             <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                             <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">

                             <div class="filter-group">
                                 <label for="filter_year">Year:</label>
                                 <select name="year" id="filter_year">
                                     <option value="">All Years</option>
                                     <?php
                                     // Get distinct years from database (exclude invalid years)
                                     $activity_years = [];
                                     if ($conn) {
                                         $year_query = "SELECT DISTINCT YEAR(start) as year FROM tblactivity WHERE project = ? AND start IS NOT NULL AND YEAR(start) > 0 AND YEAR(start) >= 2000 AND YEAR(start) <= 2100 ORDER BY year DESC";
                                         $stmt_year = mysqli_prepare($conn, $year_query);
                                         if ($stmt_year) {
                                             mysqli_stmt_bind_param($stmt_year, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_year)) {
                                                 $result_year = mysqli_stmt_get_result($stmt_year);
                                                 while ($row = mysqli_fetch_assoc($result_year)) {
                                                     $year = (int)$row['year'];
                                                     if ($year > 0 && $year >= 2000 && $year <= 2100) {
                                                         $activity_years[] = $year;
                                                     }
                                                 }
                                                 mysqli_free_result($result_year);
                                             }
                                             mysqli_stmt_close($stmt_year);
                                         }
                                     }

                                     // If no years found, use current year as fallback
                                     if (empty($activity_years)) {
                                         $activity_years[] = (int)date('Y');
                                     }

                                     foreach ($activity_years as $year):
                                     ?>
                                         <option value="<?php echo $year; ?>" <?php echo (isset($_GET['year']) && $_GET['year'] == $year) ? 'selected' : ''; ?>><?php echo $year; ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_sector">Sector:</label>
                                 <select name="sector" id="filter_sector">
                                     <option value="">All Sectors</option>
                                     <?php
                                     // Get distinct sectors from database
                                     $sectors = [];
                                     if ($conn) {
                                         $sector_query = "SELECT DISTINCT sector FROM tblactivity WHERE project = ? AND sector IS NOT NULL AND sector != '' ORDER BY sector";
                                         $stmt_sector = mysqli_prepare($conn, $sector_query);
                                         if ($stmt_sector) {
                                             mysqli_stmt_bind_param($stmt_sector, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_sector)) {
                                                 $result_sector = mysqli_stmt_get_result($stmt_sector);
                                                 while ($row = mysqli_fetch_assoc($result_sector)) {
                                                     $sectors[] = $row['sector'];
                                                 }
                                                 mysqli_free_result($result_sector);
                                             }
                                             mysqli_stmt_close($stmt_sector);
                                         }
                                     }
                                     foreach ($sectors as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['sector']) && $_GET['sector'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_indicator">Indicator:</label>
                                 <select name="indicator" id="filter_indicator">
                                     <option value="">All Indicators</option>
                                     <?php
                                     // Get distinct indicators from database
                                     $indicators = [];
                                     if ($conn) {
                                         $indicator_query = "SELECT DISTINCT indicator FROM tblactivity WHERE project = ? AND indicator IS NOT NULL AND indicator != '' ORDER BY indicator";
                                         $stmt_indicator = mysqli_prepare($conn, $indicator_query);
                                         if ($stmt_indicator) {
                                             mysqli_stmt_bind_param($stmt_indicator, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_indicator)) {
                                                 $result_indicator = mysqli_stmt_get_result($stmt_indicator);
                                                 while ($row = mysqli_fetch_assoc($result_indicator)) {
                                                     $indicators[] = $row['indicator'];
                                                 }
                                                 mysqli_free_result($result_indicator);
                                             }
                                             mysqli_stmt_close($stmt_indicator);
                                         }
                                     }
                                     foreach ($indicators as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['indicator']) && $_GET['indicator'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_municipality">Municipality:</label>
                                 <select name="municipality" id="filter_municipality">
                                     <option value="">All Municipalities</option>
                                     <?php
                                     // Get distinct municipalities from database
                                     $municipalities = [];
                                     if ($conn) {
                                         $municipality_query = "SELECT DISTINCT municipality FROM tblactivity WHERE project = ? AND municipality IS NOT NULL AND municipality != '' ORDER BY municipality";
                                         $stmt_municipality = mysqli_prepare($conn, $municipality_query);
                                         if ($stmt_municipality) {
                                             mysqli_stmt_bind_param($stmt_municipality, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_municipality)) {
                                                 $result_municipality = mysqli_stmt_get_result($stmt_municipality);
                                                 while ($row = mysqli_fetch_assoc($result_municipality)) {
                                                     $municipalities[] = $row['municipality'];
                                                 }
                                                 mysqli_free_result($result_municipality);
                                             }
                                             mysqli_stmt_close($stmt_municipality);
                                         }
                                     }
                                     foreach ($municipalities as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['municipality']) && $_GET['municipality'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>

                             <div class="filter-actions">
                                 <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                 <a href="ilcdb.php?tab=activities&limit=<?php echo $results_per_page_activity; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a> <!-- <-- Changed Link -->
                             </div>
                         </form>
                     </div>

                     <div class="table-container"> <table> <thead> <tr> <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxActivity" class="select-all-checkbox" data-type="activity" title="Select/Deselect All Activities"></th> <th class="col-rownum">#</th> <th>Start Date</th><th>End Date</th><th>Project</th><th>Subproject</th><th>Activity</th><th>Indicator</th><th>Training?</th><th>Municipality</th><th>District</th><th>Barangay</th><th>Agency</th><th>Mode</th><th>Sector</th><th>Person(s)</th><th>Resource Person(s)</th><th>Participants</th><th>Completers</th><th>Male</th><th>Female</th><th>Approved?</th><th>MOV</th><th>Remarks</th> </tr> </thead> <tbody id="activityTableBody"> <?php if ($activities_fetch_error && !$activities_result): ?> <tr> <td colspan="<?php echo $table_colspan_activity; ?>" class="table-message error"> <?php echo htmlspecialchars($activities_fetch_error); ?> </td> </tr> <?php elseif ($activities_result && mysqli_num_rows($activities_result) > 0): $row_index_activity = 0; while ($activity = mysqli_fetch_assoc($activities_result)): $row_number_activity = $offset_activity + $row_index_activity + 1; ?> <tr data-activity-id="<?php echo htmlspecialchars($activity['id']); ?>"> <td class="col-checkbox"><input type="checkbox" class="row-checkbox activity-row-checkbox" name="activity_ids[]" value="<?php echo htmlspecialchars($activity['id']); ?>"></td> <td class="col-rownum"><?php echo $row_number_activity; ?></td> <td class="date-col"><?php echo formatDateForDisplay($activity['start']); ?></td> <td class="date-col"><?php echo formatDateForDisplay($activity['end']); ?></td> <td><?php echo htmlspecialchars($activity['project'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['subproject'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['activity'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['indicator'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['training'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['municipality'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['district'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['barangay'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['agency'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['mode'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['sector'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['person'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['resource'] ?? ''); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['participants'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['completers'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['male'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['female'] ?? 0)); ?></td> <td><?php echo htmlspecialchars($activity['approved'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['mov'] ?? ''); ?></td> <td><?php echo nl2br(htmlspecialchars($activity['remarks'] ?? '')); ?></td> </tr> <?php $row_index_activity++; endwhile; else: ?> <tr> <td colspan="<?php echo $table_colspan_activity; ?>" class="table-message"> <?php echo htmlspecialchars($activities_fetch_error); ?> </td> </tr> <?php endif; if ($activities_result && is_object($activities_result)) mysqli_free_result($activities_result); ?> </tbody> </table> </div>
                     <div class="pagination-controls"> <div class="pagination-info"> <?php if ($total_activities > 0) { echo "Showing " . ($offset_activity + 1) . " - " . min($offset_activity + $results_per_page_activity, $total_activities) . " of " . number_format($total_activities); } else { echo "Showing 0 - 0 of 0"; } ?> </div> <div class="pagination-nav"> <?php $page_link_base_activity = "ilcdb.php?page="; if ($current_page_activity > 1): ?> <a href="<?php echo $page_link_base_activity . ($current_page_activity - 1) . $url_params_activity; ?>" class="btn btn-nav">Previous</a> <?php else: ?> <span class="btn btn-nav disabled">Previous</span> <?php endif; if ($total_pages_activity > 1) { $max_links = 5; $start_link = max(1, $current_page_activity - floor($max_links / 2)); $end_link = min($total_pages_activity, $start_link + $max_links - 1); if ($end_link == $total_pages_activity) $start_link = max(1, $total_pages_activity - $max_links + 1); if ($start_link > 1) { echo '<a href="'.$page_link_base_activity.'1'.$url_params_activity.'" class="btn btn-page">1</a>'; if ($start_link > 2) echo '<span class="btn btn-page disabled">...</span>'; } for ($i = $start_link; $i <= $end_link; $i++): echo '<a href="'.$page_link_base_activity.$i.$url_params_activity.'" class="btn btn-page '.($i == $current_page_activity ? 'active' : '').'">'.$i.'</a>'; endfor; if ($end_link < $total_pages_activity) { if ($end_link < $total_pages_activity - 1) echo '<span class="btn btn-page disabled">...</span>'; echo '<a href="'.$page_link_base_activity.$total_pages_activity.$url_params_activity.'" class="btn btn-page">'.$total_pages_activity.'</a>'; } } elseif ($total_activities > 0 && $total_pages_activity == 1) { echo '<a href="'.$page_link_base_activity.'1'.$url_params_activity.'" class="btn btn-page active">1</a>'; } if ($current_page_activity < $total_pages_activity): ?> <a href="<?php echo $page_link_base_activity . ($current_page_activity + 1) . $url_params_activity; ?>" class="btn btn-nav">Next</a> <?php else: ?> <span class="btn btn-nav disabled">Next</span> <?php endif; ?> </div> </div> <!-- <-- Changed Base URL -->
                </section>
            </div>

             <!-- Participants Tab -->
            <div class="tab-content <?php echo ($active_tab === 'participants') ? 'active' : ''; ?>" id="participants-tab">
                 <section class="stats-cards">
                     <?php if (!empty($participant_stats)): foreach ($participant_stats as $stat): ?>
                         <?php $insight = $stat['insight'] ?? []; ?>
                         <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                             <div class="card-header">
                                 <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                 <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                     <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                 </div>
                             </div>
                             <div class="card-body">
                                 <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                 <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                     <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                         <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                         <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                     </div>
                                 <?php endif; ?>
                             </div>
                         </div>
                     <?php endforeach; else: ?>
                         <div class="stat-card">
                             <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                             <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                         </div>
                     <?php endif; ?>
                 </section>
                 <section class="recent-participants card">
                      <div class="participants-header"> <h2><?php echo !empty($search_term_participant) || count(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'p_'.$c, $filter_columns_participant))))) > 0 ? 'Filtered Free Wifi for All Participants' : 'All Free Wifi for All Participants'; ?></h2> <button id="addParticipantButton" class="btn btn-primary"> <i class="fas fa-user-plus"></i> Add Participant </button> </div> <!-- <-- Changed Title -->
                      <div class="table-controls">
                         <form action="ilcdb.php" method="get" id="resultsPerPageFormParticipant" class="results-per-page-form"> <input type="hidden" name="tab" value="participants"> <?php if (!empty($search_term_participant)): ?> <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($search_term_participant); ?>"> <?php endif; ?> <input type="hidden" name="p_page" value="1"> <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?> <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>"> <?php endif; endforeach; ?> <!-- Carry over activity params --> <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"> <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>"> <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>"> <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>"> <!-- Carry over year --> <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?> <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>"> <?php endif; endforeach; ?> <label for="resultsPerPageSelectParticipant">Result per page:</label> <select name="p_limit" id="resultsPerPageSelectParticipant" onchange="this.form.submit();"> <?php foreach($results_per_page_options as $option): ?> <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_participant) echo 'selected'; ?>><?php echo $option; ?></option> <?php endforeach; ?> </select> </form> <!-- <-- Changed Action -->
                         <div class="table-right-controls">
                            <form action="ilcdb.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="p_search" name="p_search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_participant); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="participants">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="p_limit" value="<?php echo $results_per_page_participant; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_participant as $column): ?>
                                        <?php if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                         </div>
                      </div>

                      <!-- Filter Dropdown Area -->
                      <div id="participantFilters" class="filter-dropdown-container" style="display: none;">
                          <form action="ilcdb.php" method="get" class="filter-form"> <!-- <-- Changed Action -->
                              <input type="hidden" name="tab" value="participants">
                              <!-- Carry over other params -->
                              <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($search_term_participant); ?>">
                              <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($results_per_page_participant); ?>">
                              <input type="hidden" name="p_page" value="1">
                              <!-- Carry over activity params -->
                              <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                              <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                              <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">

                              <div class="filter-group">
                                  <label for="filter_p_year">Year:</label>
                                  <select name="p_year" id="filter_p_year">
                                      <option value="">All Years</option>
                                      <?php
                                      // Get distinct years from database (exclude invalid years)
                                      $participant_years = [];
                                      if ($conn) {
                                          $p_year_query = "SELECT DISTINCT YEAR(start) as year FROM tblparticipant WHERE project = ? AND start IS NOT NULL AND YEAR(start) > 0 AND YEAR(start) >= 2000 AND YEAR(start) <= 2100 ORDER BY year DESC";
                                          $stmt_p_year = mysqli_prepare($conn, $p_year_query);
                                          if ($stmt_p_year) {
                                              mysqli_stmt_bind_param($stmt_p_year, "s", $project_filter_value);
                                              if (mysqli_stmt_execute($stmt_p_year)) {
                                                  $result_p_year = mysqli_stmt_get_result($stmt_p_year);
                                                  while ($row = mysqli_fetch_assoc($result_p_year)) {
                                                      $year = (int)$row['year'];
                                                      if ($year > 0 && $year >= 2000 && $year <= 2100) {
                                                          $participant_years[] = $year;
                                                      }
                                                  }
                                                  mysqli_free_result($result_p_year);
                                              }
                                              mysqli_stmt_close($stmt_p_year);
                                          }
                                      }

                                      // If no years found, use current year as fallback
                                      if (empty($participant_years)) {
                                          $participant_years[] = (int)date('Y');
                                      }

                                      foreach ($participant_years as $year):
                                      ?>
                                          <option value="<?php echo $year; ?>" <?php echo (isset($_GET['p_year']) && $_GET['p_year'] == $year) ? 'selected' : ''; ?>><?php echo $year; ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_indicator">Indicator:</label>
                                  <select name="p_indicator" id="filter_p_indicator">
                                      <option value="">All Indicators</option>
                                      <?php
                                      // Get distinct indicators from database
                                      $p_indicators = [];
                                      if ($conn) {
                                          $p_indicator_query = "SELECT DISTINCT indicator FROM tblparticipant WHERE project = ? AND indicator IS NOT NULL AND indicator != '' ORDER BY indicator";
                                          $stmt_p_indicator = mysqli_prepare($conn, $p_indicator_query);
                                          if ($stmt_p_indicator) {
                                              mysqli_stmt_bind_param($stmt_p_indicator, "s", $project_filter_value);
                                              if (mysqli_stmt_execute($stmt_p_indicator)) {
                                                  $result_p_indicator = mysqli_stmt_get_result($stmt_p_indicator);
                                                  while ($row = mysqli_fetch_assoc($result_p_indicator)) {
                                                      $p_indicators[] = $row['indicator'];
                                                  }
                                                  mysqli_free_result($result_p_indicator);
                                              }
                                              mysqli_stmt_close($stmt_p_indicator);
                                          }
                                      }
                                      foreach ($p_indicators as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_indicator']) && $_GET['p_indicator'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_mode">Mode:</label>
                                  <select name="p_mode" id="filter_p_mode">
                                      <option value="">All Modes</option>
                                      <?php
                                      // Get distinct modes from database
                                      $p_modes = [];
                                      if ($conn) {
                                          $p_mode_query = "SELECT DISTINCT mode FROM tblparticipant WHERE project = ? AND mode IS NOT NULL AND mode != '' ORDER BY mode";
                                          $stmt_p_mode = mysqli_prepare($conn, $p_mode_query);
                                          if ($stmt_p_mode) {
                                              mysqli_stmt_bind_param($stmt_p_mode, "s", $project_filter_value);
                                              if (mysqli_stmt_execute($stmt_p_mode)) {
                                                  $result_p_mode = mysqli_stmt_get_result($stmt_p_mode);
                                                  while ($row = mysqli_fetch_assoc($result_p_mode)) {
                                                      $p_modes[] = $row['mode'];
                                                  }
                                                  mysqli_free_result($result_p_mode);
                                              }
                                              mysqli_stmt_close($stmt_p_mode);
                                          }
                                      }
                                      foreach ($p_modes as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_mode']) && $_GET['p_mode'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_agency">Agency:</label>
                                  <select name="p_agency" id="filter_p_agency">
                                      <option value="">All Agencies</option>
                                      <?php
                                      // Get distinct agencies from database
                                      $p_agencies = [];
                                      if ($conn) {
                                          $p_agency_query = "SELECT DISTINCT agency FROM tblparticipant WHERE project = ? AND agency IS NOT NULL AND agency != '' ORDER BY agency";
                                          $stmt_p_agency = mysqli_prepare($conn, $p_agency_query);
                                          if ($stmt_p_agency) {
                                              mysqli_stmt_bind_param($stmt_p_agency, "s", $project_filter_value);
                                              if (mysqli_stmt_execute($stmt_p_agency)) {
                                                  $result_p_agency = mysqli_stmt_get_result($stmt_p_agency);
                                                  while ($row = mysqli_fetch_assoc($result_p_agency)) {
                                                      $p_agencies[] = $row['agency'];
                                                  }
                                                  mysqli_free_result($result_p_agency);
                                              }
                                              mysqli_stmt_close($stmt_p_agency);
                                          }
                                      }
                                      foreach ($p_agencies as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_agency']) && $_GET['p_agency'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>

                              <div class="filter-actions">
                                  <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                  <a href="ilcdb.php?tab=participants&p_limit=<?php echo $results_per_page_participant; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a> <!-- <-- Changed Link -->
                              </div>
                          </form>
                      </div>

                      <div class="table-container"> <table> <thead> <tr> <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxParticipant" class="select-all-checkbox" data-type="participant" title="Select/Deselect All Participants"></th> <th class="col-rownum">#</th> <th>Start Date</th><th>End Date</th><th>Activity</th><th>Indicator</th><th>Full Name</th><th>Sex</th><th>Contact</th><th>Email</th><th>Mode</th><th>Agency</th><th>Sector</th><th>Project</th><th>Resp. Person</th><th>Remarks</th> </tr> </thead> <tbody id="participantTableBody"> <?php if ($participants_fetch_error && !$participants_result): ?> <tr> <td colspan="<?php echo $table_colspan_participant; ?>" class="table-message error"> <?php echo htmlspecialchars($participants_fetch_error); ?> </td> </tr> <?php elseif ($participants_result && mysqli_num_rows($participants_result) > 0): $row_index_participant = 0; while ($participant = mysqli_fetch_assoc($participants_result)): $row_number_participant = $offset_participant + $row_index_participant + 1; ?> <tr data-participant-id="<?php echo htmlspecialchars($participant['id']); ?>"> <td class="col-checkbox"><input type="checkbox" class="row-checkbox participant-row-checkbox" name="participant_ids[]" value="<?php echo htmlspecialchars($participant['id']); ?>"></td> <td class="col-rownum"><?php echo $row_number_participant; ?></td> <td class="date-col"><?php echo formatDateForDisplay($participant['start']); ?></td> <td class="date-col"><?php echo formatDateForDisplay($participant['end']); ?></td> <td><?php echo htmlspecialchars($participant['activity'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['indicator'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['fullname'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['sex'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['contact'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['email'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['mode'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['agency'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['sector'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['project'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['person'] ?? ''); ?></td> <td><?php echo nl2br(htmlspecialchars($participant['remarks'] ?? '')); ?></td> </tr> <?php $row_index_participant++; endwhile; else: ?> <tr> <td colspan="<?php echo $table_colspan_participant; ?>" class="table-message"> <?php echo htmlspecialchars($participants_fetch_error); ?> </td> </tr> <?php endif; if ($participants_result && is_object($participants_result)) mysqli_free_result($participants_result); ?> </tbody> </table> </div>
                      <div class="pagination-controls"> <div class="pagination-info"> <?php if ($total_participants > 0) { echo "Showing " . ($offset_participant + 1) . " - " . min($offset_participant + $results_per_page_participant, $total_participants) . " of " . number_format($total_participants); } else { echo "Showing 0 - 0 of 0"; } ?> </div> <div class="pagination-nav"> <?php $page_link_base_participant = "ilcdb.php?p_page="; if ($current_page_participant > 1): ?> <a href="<?php echo $page_link_base_participant . ($current_page_participant - 1) . $url_params_participant; ?>" class="btn btn-nav">Previous</a> <?php else: ?> <span class="btn btn-nav disabled">Previous</span> <?php endif; if ($total_pages_participant > 1) { $max_links = 5; $start_link = max(1, $current_page_participant - floor($max_links / 2)); $end_link = min($total_pages_participant, $start_link + $max_links - 1); if ($end_link == $total_pages_participant) $start_link = max(1, $total_pages_participant - $max_links + 1); if ($start_link > 1) { echo '<a href="'.$page_link_base_participant.'1'.$url_params_participant.'" class="btn btn-page">1</a>'; if ($start_link > 2) echo '<span class="btn btn-page disabled">...</span>'; } for ($i = $start_link; $i <= $end_link; $i++): echo '<a href="'.$page_link_base_participant.$i.$url_params_participant.'" class="btn btn-page '.($i == $current_page_participant ? 'active' : '').'">'.$i.'</a>'; endfor; if ($end_link < $total_pages_participant) { if ($end_link < $total_pages_participant - 1) echo '<span class="btn btn-page disabled">...</span>'; echo '<a href="'.$page_link_base_participant.$total_pages_participant.$url_params_participant.'" class="btn btn-page">'.$total_pages_participant.'</a>'; } } elseif ($total_participants > 0 && $total_pages_participant == 1) { echo '<a href="'.$page_link_base_participant.'1'.$url_params_participant.'" class="btn btn-page active">1</a>'; } if ($current_page_participant < $total_pages_participant): ?> <a href="<?php echo $page_link_base_participant . ($current_page_participant + 1) . $url_params_participant; ?>" class="btn btn-nav">Next</a> <?php else: ?> <span class="btn btn-nav disabled">Next</span> <?php endif; ?> </div> </div> <!-- <-- Changed Base URL -->
                 </section>
            </div>

             <!-- Reports Tab -->
            <div class="tab-content <?php echo ($active_tab === 'reports') ? 'active' : ''; ?>" id="reports-tab">
                 <!-- Container for Graphs -->
                 <div id="reportGraphContainer" class="card" style="background-color: transparent; border: none; box-shadow: none; padding:0;">
                     <div class="loading-indicator active" id="reportLoadingIndicator" style="padding: 40px; background-color: var(--bg-light); border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                         <i class="fas fa-spinner fa-spin"></i> Loading report data for <?php echo $selected_report_year; ?>...
                     </div>
                     <!-- Graphs will be inserted here by JavaScript -->
                 </div>
            </div>
            <!-- Tech4ED DTC Tab -->
            <div class="tab-content <?php echo ($active_tab === 'tech4ed') ? 'active' : ''; ?>" id="tech4ed-tab">
                <!-- Tech4ED Stats -->
                <section class="stats-cards">
                    <?php if (!empty($tech4ed_stats)): foreach ($tech4ed_stats as $stat): ?>
                        <?php $insight = $stat['insight'] ?? []; ?>
                        <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                            <div class="card-header">
                                <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                    <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                    <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                        <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                        <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="stat-card">
                            <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                            <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                        </div>
                    <?php endif; ?>
                </section>

                <section class="recent-tech4ed card">
                    <div class="tech4ed-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 10px;">
                        <h2><?php echo !empty($search_term_tech4ed) || count(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 't_'.$c, $filter_columns_tech4ed))))) > 0 ? 'Filtered Tech4ED DTCs' : 'All Tech4ED Centers'; ?></h2>
                        <button id="addTech4edButton" class="btn btn-primary" style="white-space: nowrap;">
                            <i class="fas fa-plus"></i> Add Tech4ED DTC
                        </button>
                    </div>
                    <div class="table-controls">
                        <form action="ilcdb.php" method="get" id="resultsPerPageFormTech4ed" class="results-per-page-form">
                            <input type="hidden" name="tab" value="tech4ed">
                            <?php if (!empty($search_term_tech4ed)): ?>
                                <input type="hidden" name="t_search" value="<?php echo htmlspecialchars($search_term_tech4ed); ?>">
                            <?php endif; ?>
                            <?php foreach ($filter_columns_tech4ed as $column): if (isset($_GET['t_'.$column]) && trim($_GET['t_'.$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo 't_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['t_'.$column])); ?>">
                            <?php endif; endforeach; ?>
                            <input type="hidden" name="t_page" value="1">
                            <!-- Carry over other tabs params -->
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">
                            <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                            <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">
                            <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>">
                            <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                            <?php endif; endforeach; ?>
                            <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>">
                            <?php endif; endforeach; ?>
                            <label for="resultsPerPageSelectTech4ed">Result per page:</label>
                            <select name="t_limit" id="resultsPerPageSelectTech4ed" onchange="this.form.submit();">
                                <?php foreach($results_per_page_options as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_tech4ed) echo 'selected'; ?>><?php echo $option; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </form>
                        <div class="table-right-controls">
                            <form action="ilcdb.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="t_search" name="t_search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_tech4ed); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="tech4ed">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="t_limit" value="<?php echo $results_per_page_tech4ed; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_tech4ed as $column): ?>
                                        <?php if (isset($_GET['t_'.$column]) && trim($_GET['t_'.$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo 't_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['t_'.$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                        </div>
                    </div>

                    <!-- Filter Dropdown Area -->
                    <div id="tech4edFilters" class="filter-dropdown-container" style="display: none;">
                        <form action="ilcdb.php" method="get" class="filter-form">
                            <input type="hidden" name="tab" value="tech4ed">
                            <!-- Carry over other params -->
                            <input type="hidden" name="t_search" value="<?php echo htmlspecialchars($search_term_tech4ed); ?>">
                            <input type="hidden" name="t_limit" value="<?php echo htmlspecialchars($results_per_page_tech4ed); ?>">
                            <input type="hidden" name="t_page" value="1">
                            <!-- Carry over other tabs params -->
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">
                            <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                            <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">

                            <div class="filter-group">
                                <label for="filter_t_category">Category:</label>
                                <select name="t_category" id="filter_t_category">
                                    <option value="">All Categories</option>
                                    <?php
                                    // Get distinct categories from database
                                    $t_categories = [];
                                    if ($conn) {
                                        $t_category_query = "SELECT DISTINCT category FROM tbltech4ed WHERE category IS NOT NULL AND category != '' ORDER BY category";
                                        $stmt_t_category = mysqli_prepare($conn, $t_category_query);
                                        if ($stmt_t_category) {
                                            if (mysqli_stmt_execute($stmt_t_category)) {
                                                $result_t_category = mysqli_stmt_get_result($stmt_t_category);
                                                while ($row = mysqli_fetch_assoc($result_t_category)) {
                                                    $t_categories[] = $row['category'];
                                                }
                                                mysqli_free_result($result_t_category);
                                            }
                                            mysqli_stmt_close($stmt_t_category);
                                        }
                                    }
                                    foreach ($t_categories as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['t_category']) && $_GET['t_category'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_t_district">District:</label>
                                <select name="t_district" id="filter_t_district">
                                    <option value="">All Districts</option>
                                    <?php
                                    // Get distinct districts from database
                                    $t_districts = [];
                                    if ($conn) {
                                        $t_district_query = "SELECT DISTINCT district FROM tbltech4ed WHERE district IS NOT NULL AND district != '' ORDER BY district";
                                        $stmt_t_district = mysqli_prepare($conn, $t_district_query);
                                        if ($stmt_t_district) {
                                            if (mysqli_stmt_execute($stmt_t_district)) {
                                                $result_t_district = mysqli_stmt_get_result($stmt_t_district);
                                                while ($row = mysqli_fetch_assoc($result_t_district)) {
                                                    $t_districts[] = $row['district'];
                                                }
                                                mysqli_free_result($result_t_district);
                                            }
                                            mysqli_stmt_close($stmt_t_district);
                                        }
                                    }
                                    foreach ($t_districts as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['t_district']) && $_GET['t_district'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_t_municipality">Municipality:</label>
                                <select name="t_municipality" id="filter_t_municipality">
                                    <option value="">All Municipalities</option>
                                    <?php
                                    // Get distinct municipalities from database
                                    $t_municipalities = [];
                                    if ($conn) {
                                        $t_municipality_query = "SELECT DISTINCT municipality FROM tbltech4ed WHERE municipality IS NOT NULL AND municipality != '' ORDER BY municipality";
                                        $stmt_t_municipality = mysqli_prepare($conn, $t_municipality_query);
                                        if ($stmt_t_municipality) {
                                            if (mysqli_stmt_execute($stmt_t_municipality)) {
                                                $result_t_municipality = mysqli_stmt_get_result($stmt_t_municipality);
                                                while ($row = mysqli_fetch_assoc($result_t_municipality)) {
                                                    $t_municipalities[] = $row['municipality'];
                                                }
                                                mysqli_free_result($result_t_municipality);
                                            }
                                            mysqli_stmt_close($stmt_t_municipality);
                                        }
                                    }
                                    foreach ($t_municipalities as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['t_municipality']) && $_GET['t_municipality'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_t_status">Status:</label>
                                <select name="t_status" id="filter_t_status">
                                    <option value="">All Status</option>
                                    <?php
                                    // Get distinct status from database
                                    $t_statuses = [];
                                    if ($conn) {
                                        $t_status_query = "SELECT DISTINCT status FROM tbltech4ed WHERE status IS NOT NULL AND status != '' ORDER BY status";
                                        $stmt_t_status = mysqli_prepare($conn, $t_status_query);
                                        if ($stmt_t_status) {
                                            if (mysqli_stmt_execute($stmt_t_status)) {
                                                $result_t_status = mysqli_stmt_get_result($stmt_t_status);
                                                while ($row = mysqli_fetch_assoc($result_t_status)) {
                                                    $t_statuses[] = $row['status'];
                                                }
                                                mysqli_free_result($result_t_status);
                                            }
                                            mysqli_stmt_close($stmt_t_status);
                                        }
                                    }
                                    foreach ($t_statuses as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['t_status']) && $_GET['t_status'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_t_connectivity">Connectivity:</label>
                                <select name="t_connectivity" id="filter_t_connectivity">
                                    <option value="">All Connectivity</option>
                                    <?php
                                    // Get distinct connectivity from database
                                    $t_connectivities = [];
                                    if ($conn) {
                                        $t_connectivity_query = "SELECT DISTINCT connectivity FROM tbltech4ed WHERE connectivity IS NOT NULL AND connectivity != '' ORDER BY connectivity";
                                        $stmt_t_connectivity = mysqli_prepare($conn, $t_connectivity_query);
                                        if ($stmt_t_connectivity) {
                                            if (mysqli_stmt_execute($stmt_t_connectivity)) {
                                                $result_t_connectivity = mysqli_stmt_get_result($stmt_t_connectivity);
                                                while ($row = mysqli_fetch_assoc($result_t_connectivity)) {
                                                    $t_connectivities[] = $row['connectivity'];
                                                }
                                                mysqli_free_result($result_t_connectivity);
                                            }
                                            mysqli_stmt_close($stmt_t_connectivity);
                                        }
                                    }
                                    foreach ($t_connectivities as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['t_connectivity']) && $_GET['t_connectivity'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                <a href="ilcdb.php?tab=tech4ed&t_limit=<?php echo $results_per_page_tech4ed; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a>
                            </div>
                        </form>
                    </div>

                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxTech4ed" class="select-all-checkbox" data-type="tech4ed" title="Select/Deselect All Tech4ED DTCs"></th>
                                    <th class="col-rownum">#</th>
                                    <th>Region</th>
                                    <th>Province</th>
                                    <th>District</th>
                                    <th>Municipality</th>
                                    <th>Barangay</th>
                                    <th>Street</th>
                                    <th>Location</th>
                                    <th>Center Name</th>
                                    <th>Host</th>
                                    <th>Category</th>
                                    <th>Longitude</th>
                                    <th>Latitude</th>
                                    <th>Center Manager</th>
                                    <th>CM Email</th>
                                    <th>CM Mobile</th>
                                    <th>CM Landline</th>
                                    <th>CM Gender</th>
                                    <th>Asst. Manager</th>
                                    <th>AM Email</th>
                                    <th>AM Mobile</th>
                                    <th>AM Landline</th>
                                    <th>AM Gender</th>
                                    <th>Launch Date</th>
                                    <th>Registration Date</th>
                                    <th>Operation Date</th>
                                    <th>Last Visited</th>
                                    <th>Desktop</th>
                                    <th>Laptop</th>
                                    <th>Printer</th>
                                    <th>Scanner</th>
                                    <th>Status</th>
                                    <th>Network</th>
                                    <th>Connectivity</th>
                                    <th>Speed</th>
                                    <th>CM Male Trainees</th>
                                    <th>CM Female Trainees</th>
                                    <th>S-Training</th>
                                    <th>E-Training</th>
                                    <th>Signing</th>
                                    <th>Partner</th>
                                    <th>Expiration</th>
                                    <th>Donation</th>
                                    <th>Donation Date</th>
                                    <th>TCMS</th>
                                    <th>Key One</th>
                                    <th>Identifier</th>
                                </tr>
                            </thead>
                            <tbody id="tech4edTableBody">
                                <?php if ($tech4ed_fetch_error && !$tech4ed_result): ?>
                                    <tr>
                                        <td colspan="48" class="table-message error">
                                            <?php echo htmlspecialchars($tech4ed_fetch_error); ?>
                                        </td>
                                    </tr>
                                <?php elseif ($tech4ed_result && mysqli_num_rows($tech4ed_result) > 0):
                                    $row_index_tech4ed = 0;
                                    while ($tech4ed = mysqli_fetch_assoc($tech4ed_result)):
                                        $row_number_tech4ed = $offset_tech4ed + $row_index_tech4ed + 1;
                                ?>
                                    <tr data-tech4ed-id="<?php echo htmlspecialchars($tech4ed['id']); ?>">
                                        <td class="col-checkbox"><input type="checkbox" class="row-checkbox tech4ed-row-checkbox" name="tech4ed_ids[]" value="<?php echo htmlspecialchars($tech4ed['id']); ?>"></td>
                                        <td class="col-rownum"><?php echo $row_number_tech4ed; ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['region'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['province'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['district'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['municipality'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['barangay'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['street'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['location'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cname'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['host'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['category'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['longitude'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['latitude'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cmanager'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cemail'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cmobile'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['clandline'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cgender'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['amanager'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['aemail'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['amobile'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['alandline'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['agender'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['launch'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['registration'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['operation'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['visited'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['desktop'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['laptop'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['printer'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['scanner'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['status'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['network'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['connectivity'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['speed'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cmtmale'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['cmtfemale'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['straining'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['etraining'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['signing'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['partner'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['expiration'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['donation'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($tech4ed['datedonation'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['tcms'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['key_one'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($tech4ed['identifier'] ?? ''); ?></td>
                                    </tr>
                                <?php
                                    $row_index_tech4ed++;
                                    endwhile;
                                else:
                                ?>
                                    <tr>
                                        <td colspan="48" class="table-message">
                                            <?php echo htmlspecialchars($tech4ed_fetch_error); ?>
                                        </td>
                                    </tr>
                                <?php
                                endif;
                                if ($tech4ed_result && is_object($tech4ed_result)) mysqli_free_result($tech4ed_result);
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination-controls">
                        <div class="pagination-info">
                            <?php if ($total_tech4ed > 0) {
                                echo "Showing " . ($offset_tech4ed + 1) . " - " . min($offset_tech4ed + $results_per_page_tech4ed, $total_tech4ed) . " of " . number_format($total_tech4ed);
                            } else {
                                echo "Showing 0 - 0 of 0";
                            } ?>
                        </div>
                        <div class="pagination-nav">
                            <?php
                            $page_link_base_tech4ed = "ilcdb.php?t_page=";
                            if ($current_page_tech4ed > 1):
                            ?>
                                <a href="<?php echo $page_link_base_tech4ed . ($current_page_tech4ed - 1) . $url_params_tech4ed; ?>" class="btn btn-nav">Previous</a>
                            <?php else: ?>
                                <span class="btn btn-nav disabled">Previous</span>
                            <?php endif;

                            if ($total_pages_tech4ed > 1) {
                                $max_links = 5;
                                $start_link = max(1, $current_page_tech4ed - floor($max_links / 2));
                                $end_link = min($total_pages_tech4ed, $start_link + $max_links - 1);

                                if ($end_link == $total_pages_tech4ed) {
                                    $start_link = max(1, $total_pages_tech4ed - $max_links + 1);
                                }

                                if ($start_link > 1) {
                                    echo '<a href="'.$page_link_base_tech4ed.'1'.$url_params_tech4ed.'" class="btn btn-page">1</a>';
                                    if ($start_link > 2) {
                                        echo '<span class="btn btn-page disabled">...</span>';
                                    }
                                }

                                for ($i = $start_link; $i <= $end_link; $i++):
                                    echo '<a href="'.$page_link_base_tech4ed.$i.$url_params_tech4ed.'" class="btn btn-page '.($i == $current_page_tech4ed ? 'active' : '').'">'.$i.'</a>';
                                endfor;

                                if ($end_link < $total_pages_tech4ed) {
                                    if ($end_link < $total_pages_tech4ed - 1) {
                                        echo '<span class="btn btn-page disabled">...</span>';
                                    }
                                    echo '<a href="'.$page_link_base_tech4ed.$total_pages_tech4ed.$url_params_tech4ed.'" class="btn btn-page">'.$total_pages_tech4ed.'</a>';
                                }
                            } elseif ($total_tech4ed > 0 && $total_pages_tech4ed == 1) {
                                echo '<a href="'.$page_link_base_tech4ed.'1'.$url_params_tech4ed.'" class="btn btn-page active">1</a>';
                            }

                            if ($current_page_tech4ed < $total_pages_tech4ed):
                            ?>
                                <a href="<?php echo $page_link_base_tech4ed . ($current_page_tech4ed + 1) . $url_params_tech4ed; ?>" class="btn btn-nav">Next</a>
                            <?php else: ?>
                                <span class="btn btn-nav disabled">Next</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    <!-- Action Bar -->
    <div id="selectionActionBar"> <span id="selectedCount">0 items selected</span> <div class="action-buttons"> <button id="editButton" class="btn btn-secondary" disabled title="Edit selected (1 item only)"><i class="fas fa-edit"></i> Edit</button> <button id="deleteButton" class="btn btn-delete" disabled title="Delete selected items"><i class="fas fa-trash"></i> Delete</button> <button id="uploadButton" class="btn btn-secondary" disabled title="Upload files for selected item(s)"><i class="fas fa-upload"></i> Upload Files</button> <button id="viewFilesButton" class="btn btn-secondary" disabled title="View files for selected item(s)"><i class="fas fa-folder-open"></i> View Files</button> </div> <button class="close-action-bar" title="Deselect All & Close">×</button> </div>

    <!-- ========= -->
    <!-- MODALS    -->
    <!-- ========= -->

    <!-- Add Activity Modal -->
    <div id="addModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2>Add New Activity</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <form id="addActivityForm"> <div class="form-grid"> <div class="form-field half-width"> <label for="addActStart">Start Date:</label> <input type="date" id="addActStart" name="start"> </div> <div class="form-field half-width"> <label for="addActEnd">End Date:</label> <input type="date" id="addActEnd" name="end"> </div> <div class="form-field half-width"> <label for="addActProject">Project:</label> <input type="text" id="addActProject" name="project" value="<?php echo htmlspecialchars($project_filter_value); ?>" readonly> </div> <div class="form-field half-width"> <label for="addActSubproject">Subproject:</label> <input type="text" id="addActSubproject" name="subproject"> </div> <div class="form-field full-width"> <label for="addActActivityName">Activity Name:</label> <input type="text" id="addActActivityName" name="activity" required> </div> <div class="form-field full-width"> <label for="addActIndicator">Indicator:</label> <input type="text" id="addActIndicator" name="indicator"> </div> <div class="form-field quarter-width"> <label for="addActMunicipality">Municipality:</label> <input type="text" id="addActMunicipality" name="municipality"> </div> <div class="form-field quarter-width"> <label for="addActDistrict">District:</label> <input type="text" id="addActDistrict" name="district"> </div> <div class="form-field quarter-width"> <label for="addActBarangay">Barangay:</label> <input type="text" id="addActBarangay" name="barangay"> </div> <div class="form-field quarter-width"> <label for="addActTraining">Training Venue:</label> <input type="text" id="addActTraining" name="training"> </div> <div class="form-field half-width"> <label for="addActAgency">Requesting Agency:</label> <input type="text" id="addActAgency" name="agency"> </div> <div class="form-field half-width"> <label for="addActMode">Mode:</label> <input type="text" id="addActMode" name="mode"> </div> <div class="form-field half-width"> <label for="addActSector">Target Sector:</label> <input type="text" id="addActSector" name="sector"> </div> <div class="form-field half-width"> <label for="addActApproved">Approved AD?:</label> <input type="text" id="addActApproved" name="approved"> </div> <div class="form-field half-width"> <label for="addActPerson">Responsible Person(s):</label> <input type="text" id="addActPerson" name="person"> </div> <div class="form-field half-width"> <label for="addActResource">Resource Person(s):</label> <input type="text" id="addActResource" name="resource"> </div> <div class="form-field quarter-width"> <label for="addActParticipants">Participants:</label> <input type="number" id="addActParticipants" name="participants" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActCompleters">Completers:</label> <input type="number" id="addActCompleters" name="completers" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActMale">Male:</label> <input type="number" id="addActMale" name="male" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActFemale">Female:</label> <input type="number" id="addActFemale" name="female" min="0" step="1" value="0"> </div> <div class="form-field full-width"> <label for="addActMov">Link to MOVs:</label> <input type="text" id="addActMov" name="mov"> </div> <div class="form-field remarks-field"> <label for="addActRemarks">Remarks:</label> <textarea id="addActRemarks" name="remarks" rows="3"></textarea> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="addModal">Cancel</button> <button type="submit" form="addActivityForm" class="btn btn-primary" id="saveAddActivityButton">Save Activity</button> </div> </div> </div>
    <!-- Edit Activity Modal -->
    <div id="editModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="editActivityModalTitle">Edit Activity</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <div class="loading-indicator" id="editActivityLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading activity data... </div> <div class="error-indicator" id="editActivityErrorIndicator" style="display: none;"> Error loading data. </div> <form id="editActivityForm" style="display: none;"> <input type="hidden" id="editActivityFormId" name="id"> <div class="form-grid"> <div class="form-field half-width"> <label for="editActStart">Start Date:</label> <input type="date" id="editActStart" name="start"> </div> <div class="form-field half-width"> <label for="editActEnd">End Date:</label> <input type="date" id="editActEnd" name="end"> </div> <div class="form-field half-width"> <label for="editActProject">Project:</label> <input type="text" id="editActProject" name="project" readonly> </div> <div class="form-field half-width"> <label for="editActSubproject">Subproject:</label> <input type="text" id="editActSubproject" name="subproject"> </div> <div class="form-field full-width"> <label for="editActActivityName">Activity Name:</label> <input type="text" id="editActActivityName" name="activity"> </div> <div class="form-field full-width"> <label for="editActIndicator">Indicator:</label> <input type="text" id="editActIndicator" name="indicator"> </div> <div class="form-field quarter-width"> <label for="editActMunicipality">Municipality:</label> <input type="text" id="editActMunicipality" name="municipality"> </div> <div class="form-field quarter-width"> <label for="editActDistrict">District:</label> <input type="text" id="editActDistrict" name="district"> </div> <div class="form-field quarter-width"> <label for="editActBarangay">Barangay:</label> <input type="text" id="editActBarangay" name="barangay"> </div> <div class="form-field quarter-width"> <label for="editActTraining">Training Venue:</label> <input type="text" id="editActTraining" name="training"> </div> <div class="form-field half-width"> <label for="editActAgency">Requesting Agency:</label> <input type="text" id="editActAgency" name="agency"> </div> <div class="form-field half-width"> <label for="editActMode">Mode:</label> <input type="text" id="editActMode" name="mode"> </div> <div class="form-field half-width"> <label for="editActSector">Target Sector:</label> <input type="text" id="editActSector" name="sector"> </div> <div class="form-field half-width"> <label for="editActApproved">Approved AD?:</label> <input type="text" id="editActApproved" name="approved"> </div> <div class="form-field half-width"> <label for="editActPerson">Responsible Person(s):</label> <input type="text" id="editActPerson" name="person"> </div> <div class="form-field half-width"> <label for="editActResource">Resource Person(s):</label> <input type="text" id="editActResource" name="resource"> </div> <div class="form-field quarter-width"> <label for="editActParticipants">Participants:</label> <input type="number" id="editActParticipants" name="participants" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActCompleters">Completers:</label> <input type="number" id="editActCompleters" name="completers" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActMale">Male:</label> <input type="number" id="editActMale" name="male" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActFemale">Female:</label> <input type="number" id="editActFemale" name="female" min="0" step="1" placeholder="0"> </div> <div class="form-field full-width"> <label for="editActMov">Link to MOVs:</label> <input type="text" id="editActMov" name="mov"> </div> <div class="form-field remarks-field"> <label for="editActRemarks">Remarks:</label> <textarea id="editActRemarks" name="remarks" rows="3"></textarea> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="editModal">Cancel</button> <button type="submit" form="editActivityForm" class="btn btn-primary" id="saveEditActivityButton" disabled>Save Changes</button> </div> </div> </div>
    <!-- Add Participant Modal -->
    <div id="addParticipantModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2>Add New Participant</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <form id="addParticipantForm"> <div class="form-grid"> <div class="form-field half-width"> <label for="addPartStart">Start Date:</label> <input type="date" id="addPartStart" name="start"> </div> <div class="form-field half-width"> <label for="addPartEnd">End Date:</label> <input type="date" id="addPartEnd" name="end"> </div> <div class="form-field half-width"> <label for="addPartProject">Project:</label> <input type="text" id="addPartProject" name="project" value="<?php echo htmlspecialchars($project_filter_value); ?>" readonly> </div> <div class="form-field half-width"> <label for="addPartActivity">Activity Name:</label> <input type="text" id="addPartActivity" name="activity" required> </div> <div class="form-field full-width"> <label for="addPartIndicator">Indicator:</label> <input type="text" id="addPartIndicator" name="indicator"> </div> <div class="form-field full-width"> <label for="addPartFullname">Full Name:</label> <input type="text" id="addPartFullname" name="fullname" required> </div> <div class="form-field half-width"> <label for="addPartSex">Sex:</label> <select id="addPartSex" name="sex"> <option value="">Select...</option> <option value="Male">Male</option> <option value="Female">Female</option> </select> </div> <div class="form-field half-width"> <label for="addPartContact">Contact Number:</label> <input type="text" id="addPartContact" name="contact"> </div> <div class="form-field half-width"> <label for="addPartEmail">Email Address:</label> <input type="email" id="addPartEmail" name="email" pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"> </div> <div class="form-field half-width"> <label for="addPartAgency">Agency/Organization:</label> <input type="text" id="addPartAgency" name="agency"> </div> <div class="form-field half-width"> <label for="addPartSector">Sector:</label> <input type="text" id="addPartSector" name="sector"> </div> <div class="form-field half-width"> <label for="addPartMode">Mode:</label> <input type="text" id="addPartMode" name="mode"> </div> <div class="form-field half-width"> <label for="addPartPerson">Responsible Person:</label> <input type="text" id="addPartPerson" name="person"> </div> <div class="form-field half-width"> <label for="addPartRemarks">Remarks:</label> <input type="text" id="addPartRemarks" name="remarks"> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="addParticipantModal">Cancel</button> <button type="submit" form="addParticipantForm" class="btn btn-primary" id="saveAddParticipantButton">Save Participant</button> </div> </div> </div>
    <!-- Edit Participant Modal -->
    <div id="editParticipantModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="editParticipantModalTitle">Edit Participant</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <div class="loading-indicator" id="editParticipantLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading data... </div> <div class="error-indicator" id="editParticipantErrorIndicator" style="display: none;"> Error loading data. </div> <form id="editParticipantForm" style="display: none;"> <input type="hidden" id="editParticipantFormId" name="id"> <div class="form-grid"> <div class="form-field half-width"> <label for="editPartStart">Start Date:</label> <input type="date" id="editPartStart" name="start"> </div> <div class="form-field half-width"> <label for="editPartEnd">End Date:</label> <input type="date" id="editPartEnd" name="end"> </div> <div class="form-field half-width"> <label for="editPartProject">Project:</label> <input type="text" id="editPartProject" name="project" readonly> </div> <div class="form-field half-width"> <label for="editPartActivity">Activity Name:</label> <input type="text" id="editPartActivity" name="activity"> </div> <div class="form-field full-width"> <label for="editPartIndicator">Indicator:</label> <input type="text" id="editPartIndicator" name="indicator"> </div> <div class="form-field full-width"> <label for="editPartFullname">Full Name:</label> <input type="text" id="editPartFullname" name="fullname"> </div> <div class="form-field half-width"> <label for="editPartSex">Sex:</label> <select id="editPartSex" name="sex"> <option value="">Select...</option> <option value="Male">Male</option> <option value="Female">Female</option> </select> </div> <div class="form-field half-width"> <label for="editPartContact">Contact Number:</label> <input type="text" id="editPartContact" name="contact"> </div> <div class="form-field half-width"> <label for="editPartEmail">Email Address:</label> <input type="email" id="editPartEmail" name="email" pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"> </div> <div class="form-field half-width"> <label for="editPartAgency">Agency/Organization:</label> <input type="text" id="editPartAgency" name="agency"> </div> <div class="form-field half-width"> <label for="editPartSector">Sector:</label> <input type="text" id="editPartSector" name="sector"> </div> <div class="form-field half-width"> <label for="editPartMode">Mode:</label> <input type="text" id="editPartMode" name="mode"> </div> <div class="form-field half-width"> <label for="editPartPerson">Responsible Person:</label> <input type="text" id="editPartPerson" name="person"> </div> <div class="form-field half-width"> <label for="editPartRemarks">Remarks:</label> <input type="text" id="editPartRemarks" name="remarks"> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="editParticipantModal">Cancel</button> <button type="submit" form="editParticipantForm" class="btn btn-primary" id="saveEditParticipantButton" disabled>Save Changes</button> </div> </div> </div>
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal"> <div class="modal-content small"> <div class="modal-header"> <h2 id="deleteModalTitle">Confirm Deletion</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <p>Are you sure you want to delete the selected <strong id="deleteItemType">item(s)</strong> (<strong id="deleteItemCount">0</strong>)?</p> <p id="deleteFileWarning" style="display: none; color: var(--text-medium); margin-top: 10px;">This will permanently remove the selected file(s).</p> <p id="deleteItemWarning" style="display: block; color: var(--red-color); margin-top: 15px;"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="deleteModal">Cancel</button> <button type="button" id="confirmDeleteButton" class="btn btn-danger">Delete</button> </div> </div> </div>
    <!-- Upload Files Modal -->
    <div id="uploadModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="uploadModalTitle">Upload Files</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <p>Select files to associate with the <strong id="uploadItemCount">0</strong> selected <strong id="uploadItemType">item(s)</strong>:</p> <form id="uploadForm" enctype="multipart/form-data"> <div class="form-group"> <label for="fileInput">Choose files:</label> <input type="file" id="fileInput" name="files[]" multiple required style="padding: 10px; border: 1px dashed #ccc; background-color: #fdfdfd; display: block; width: 100%;"> <p style="margin-top: 5px; font-size: 12px; color: var(--text-light);">(Max size: 10MB per file. Allowed types: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG)</p> <p id="uploadTypeInfo" style="margin-top: 5px; font-size: 12px; color: var(--text-light);"><i class="fas fa-info-circle"></i> Files will be associated with the selected items.</p> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="uploadModal">Cancel</button> <button type="submit" form="uploadForm" class="btn btn-primary" id="submitUploadButton"> <i class="fas fa-upload"></i> Upload </button> </div> </div> </div>
    <!-- View Files Modal -->
    <div id="viewFilesModal" class="modal large"> <div class="modal-content"> <div class="modal-header"> <h2 id="viewFilesModalTitle">View Associated Files</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body" id="viewFilesModalBody"> <div class="view-files-header"> <p id="viewFilesParentItemInfo">Files for 0 selected item(s):</p> <div class="view-files-controls"> <label class="checkbox-label"> <input type="checkbox" id="viewFilesSelectAll" disabled> Select All </label> <button id="viewFilesDeleteSelected" class="btn btn-delete btn-sm" disabled> <i class="fas fa-trash"></i> Delete Selected </button> </div> </div> <div id="fileListContainer" class="grouped"> <div class="loading-indicator active" id="viewLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading file list... </div> <div id="fileGroupContainer"> <!-- Groups injected by JS --> </div> <div id="viewFilesEmptyMessage" class="empty-message" style="display: none;"> No files associated with the selected item(s). </div> <div id="viewFilesErrorMessage" class="error-message" style="display: none;"> <i class="fas fa-exclamation-triangle"></i> Error loading files. </div> </div> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="viewFilesModal">Close</button> </div> </div> </div>
    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="exportModalTitle"><i class="fas fa-file-export"></i> Export Data</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <h4>What would you like to export?</h4>
                <div style="margin: 20px 0;">
                    <!-- Option 1: All -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="all" checked style="margin-right: 10px;">
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #e3f2fd; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-database" style="color: #2196f3; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportAllLabel">All items</strong>
                                    <div style="font-size: 13px; color: #666;">Export the complete dataset for the current tab</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <!-- Option 2: Filtered -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="filtered" style="margin-right: 10px;">
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #fff8e1; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-filter" style="color: #ffc107; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportFilteredLabel">Filtered items</strong>
                                    <div id="filteredItemsCount" style="font-size: 13px; color: #666;">0 items in current filter</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <!-- Option 3: Selected -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="selected" style="margin-right: 10px;" disabled>
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #e8f5e9; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-check" style="color: #4caf50; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportSelectedLabel">Selected items</strong>
                                    <div id="selectedItemsCount" style="font-size: 13px; color: #666;">0 items selected</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                <h4>Export format</h4>
                <div style="margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px;">
                    <label style="display: flex; align-items: center;">
                        <input type="radio" name="exportFormat" value="csv" checked style="margin-right: 10px;">
                        <div style="display: flex; align-items: center;">
                            <div style="margin-right: 15px;">
                                <i class="fas fa-file-csv" style="color: #607d8b; font-size: 24px;"></i>
                            </div>
                            <div>
                                <strong>CSV</strong>
                                <div style="font-size: 13px; color: #666;">Compatible with Excel, Google Sheets, etc.</div>
                            </div>
                        </div>
                    </label>
                </div>
                <div id="exportProcessingIndicator" class="loading-indicator" style="display: none; margin-top: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Preparing export...
                </div>
                <div id="exportErrorIndicator" class="error-indicator" style="display: none; margin-top: 15px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="exportModal">Cancel</button>
                <button type="button" id="exportDataButton" class="btn btn-primary">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>
    <!-- Import Modal (Unified) -->
    <div id="importModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="importModalTitle"><i class="fas fa-file-import"></i> Import Data</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <div id="importSteps">
                    <div id="importStep1">
                        <h4>Step 1: Upload CSV File</h4>
                        <p id="importInstructions">Select a CSV file containing the data to import.</p>
                        <div class="file-upload-area">
                            <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
                            <label for="csvFileInput" class="btn btn-secondary">
                                <i class="fas fa-upload"></i> Choose File
                            </label>
                            <span id="fileNameDisplay" class="file-name-display">No file chosen</span>
                        </div>
                        <small style="color: var(--text-light); display: block; margin-top: 5px;">Ensure the first row contains the exact header names.</small>
                        <div id="importRequiredColumnsInfo" class="required-columns-info" style="margin-top: 15px;">
                            <p><i class="fas fa-info-circle"></i> <strong>Required Columns:</strong></p>
                            <div id="importRequiredColumnsList" class="column-tags">
                                <!-- Columns will be populated by JavaScript -->
                            </div>
                            <p id="importNote" style="margin-top: 10px; font-size: 13px; display: none;"></p>
                            <p style="margin-top: 10px; font-size: 12px; color: var(--text-light);"><i class="fas fa-exclamation-circle"></i> <strong>Note:</strong> For Activities, the 'Bureau' column must match '<?php echo htmlspecialchars($project_filter_value); ?>' and will be validated during import.</p> <!-- <-- Updated project name -->
                        </div>
                        <div id="importErrorStep1" class="error-indicator" style="display: none; margin-top: 15px;"></div>
                    </div>
                    <div id="importStep2" style="display: none;">
                        <h4 style="margin-top: 20px;">Step 2: Preview Data</h4>
                        <p>Review the first few rows.</p>
                        <div id="previewLoadingIndicator" class="loading-indicator" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> Processing file...
                        </div>
                        <div id="importErrorStep2" class="error-indicator" style="display: none;"></div>
                        <div id="previewTableContainer" class="table-container" style="max-height: 300px; overflow-y: auto; margin-top: 10px; border: 1px solid var(--border-color); display: none;">
                            <table id="previewTable">
                                <thead></thead>
                                <tbody></tbody>
                            </table>
                            <p id="previewRowCount" style="text-align: center; margin-top: 10px; font-size: 12px; color: var(--text-light);"></p>
                        </div>
                    </div>
                </div>
                <div id="importProcessingIndicator" class="loading-indicator" style="display: none; margin-top: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Importing data...
                </div>
                <div id="importResultIndicator" style="display: none; margin-top: 15px; padding: 10px; border-radius: 4px; text-align: center;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="importModal">Cancel</button>
                <button type="button" id="previewDataButton" class="btn btn-secondary" disabled>
                    <i class="fas fa-eye"></i> Preview Data
                </button>
                <button type="button" id="importDataButton" class="btn btn-primary" disabled>
                    <i class="fas fa-check"></i> Import Data
                </button>
            </div>
        </div>
    </div>

    <!-- Add Tech4ED DTC Modal -->
    <div id="addTech4edModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2>Add New Tech4ED DTC</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <form id="addTech4edForm">
                    <div class="form-grid">
                        <!-- Location Information -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edRegion">Region:</label>
                            <input type="text" id="addTech4edRegion" name="region">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edProvince">Province:</label>
                            <input type="text" id="addTech4edProvince" name="province">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edDistrict">District:</label>
                            <input type="text" id="addTech4edDistrict" name="district">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edMunicipality">Municipality:</label>
                            <input type="text" id="addTech4edMunicipality" name="municipality">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edBarangay">Barangay:</label>
                            <input type="text" id="addTech4edBarangay" name="barangay">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edStreet">Street:</label>
                            <input type="text" id="addTech4edStreet" name="street">
                        </div>
                        <div class="form-field half-width">
                            <label for="addTech4edLocation">Location:</label>
                            <input type="text" id="addTech4edLocation" name="location">
                        </div>

                        <!-- Center Information -->
                        <div class="form-field half-width">
                            <label for="addTech4edCname">Center Name:</label>
                            <input type="text" id="addTech4edCname" name="cname" required>
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edHost">Host:</label>
                            <input type="text" id="addTech4edHost" name="host">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edCategory">Category:</label>
                            <input type="text" id="addTech4edCategory" name="category">
                        </div>

                        <!-- Coordinates -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edLongitude">Longitude:</label>
                            <input type="text" id="addTech4edLongitude" name="longitude">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edLatitude">Latitude:</label>
                            <input type="text" id="addTech4edLatitude" name="latitude">
                        </div>

                        <!-- Center Manager Information -->
                        <div class="form-field half-width">
                            <label for="addTech4edCmanager">Center Manager:</label>
                            <input type="text" id="addTech4edCmanager" name="cmanager">
                        </div>
                        <div class="form-field half-width">
                            <label for="addTech4edCemail">CM Email:</label>
                            <input type="email" id="addTech4edCemail" name="cemail">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edCmobile">CM Mobile:</label>
                            <input type="text" id="addTech4edCmobile" name="cmobile">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edClandline">CM Landline:</label>
                            <input type="text" id="addTech4edClandline" name="clandline">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edCgender">CM Gender:</label>
                            <select id="addTech4edCgender" name="cgender">
                                <option value="">Select...</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>

                        <!-- Assistant Manager Information -->
                        <div class="form-field half-width">
                            <label for="addTech4edAmanager">Assistant Manager:</label>
                            <input type="text" id="addTech4edAmanager" name="amanager">
                        </div>
                        <div class="form-field half-width">
                            <label for="addTech4edAemail">AM Email:</label>
                            <input type="email" id="addTech4edAemail" name="aemail">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edAmobile">AM Mobile:</label>
                            <input type="text" id="addTech4edAmobile" name="amobile">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edAlandline">AM Landline:</label>
                            <input type="text" id="addTech4edAlandline" name="alandline">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edAgender">AM Gender:</label>
                            <select id="addTech4edAgender" name="agender">
                                <option value="">Select...</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>

                        <!-- Dates -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edLaunch">Launch Date:</label>
                            <input type="date" id="addTech4edLaunch" name="launch">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edRegistration">Registration Date:</label>
                            <input type="date" id="addTech4edRegistration" name="registration">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edOperation">Operation Date:</label>
                            <input type="date" id="addTech4edOperation" name="operation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edVisited">Last Visited:</label>
                            <input type="date" id="addTech4edVisited" name="visited">
                        </div>

                        <!-- Equipment -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edDesktop">Desktop:</label>
                            <input type="number" id="addTech4edDesktop" name="desktop" min="0" step="1" value="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edLaptop">Laptop:</label>
                            <input type="number" id="addTech4edLaptop" name="laptop" min="0" step="1" value="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edPrinter">Printer:</label>
                            <input type="number" id="addTech4edPrinter" name="printer" min="0" step="1" value="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edScanner">Scanner:</label>
                            <input type="number" id="addTech4edScanner" name="scanner" min="0" step="1" value="0">
                        </div>

                        <!-- Status and Network -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edStatus">Status:</label>
                            <select id="addTech4edStatus" name="status">
                                <option value="">Select...</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                                <option value="Under Maintenance">Under Maintenance</option>
                            </select>
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edNetwork">Network:</label>
                            <input type="text" id="addTech4edNetwork" name="network">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edConnectivity">Connectivity:</label>
                            <input type="text" id="addTech4edConnectivity" name="connectivity">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edSpeed">Speed:</label>
                            <input type="text" id="addTech4edSpeed" name="speed">
                        </div>

                        <!-- Training Information -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edCmtmale">CM Male Trainees:</label>
                            <input type="number" id="addTech4edCmtmale" name="cmtmale" min="0" step="1" value="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edCmtfemale">CM Female Trainees:</label>
                            <input type="number" id="addTech4edCmtfemale" name="cmtfemale" min="0" step="1" value="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edStraining">S-Training:</label>
                            <input type="text" id="addTech4edStraining" name="straining">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edEtraining">E-Training:</label>
                            <input type="text" id="addTech4edEtraining" name="etraining">
                        </div>

                        <!-- Additional Information -->
                        <div class="form-field quarter-width">
                            <label for="addTech4edSigning">Signing:</label>
                            <input type="text" id="addTech4edSigning" name="signing">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edPartner">Partner:</label>
                            <input type="text" id="addTech4edPartner" name="partner">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edDonation">Donation:</label>
                            <input type="text" id="addTech4edDonation" name="donation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edDatedonation">Donation Date:</label>
                            <input type="date" id="addTech4edDatedonation" name="datedonation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edTcms">TCMS:</label>
                            <input type="text" id="addTech4edTcms" name="tcms">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edKeyOne">Key One:</label>
                            <input type="text" id="addTech4edKeyOne" name="key_one">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="addTech4edIdentifier">Identifier:</label>
                            <input type="text" id="addTech4edIdentifier" name="identifier">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="addTech4edModal">Cancel</button>
                <button type="submit" form="addTech4edForm" class="btn btn-primary" id="saveAddTech4edButton">
                    <i class="fas fa-save"></i> Save Tech4ED DTC
                </button>
            </div>
        </div>
    </div>

    <!-- Edit Tech4ED DTC Modal -->
    <div id="editTech4edModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="editTech4edModalTitle">Edit Tech4ED DTC</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <div id="editTech4edLoadingIndicator" class="loading-indicator">
                    <i class="fas fa-spinner fa-spin"></i> Loading Tech4ED DTC data...
                </div>
                <div id="editTech4edErrorIndicator" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load Tech4ED DTC data.</span>
                </div>
                <form id="editTech4edForm" style="display: none;">
                    <input type="hidden" id="editTech4edFormId" name="id">
                    <div class="form-grid">
                        <!-- Location Information -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edRegion">Region:</label>
                            <input type="text" id="editTech4edRegion" name="region">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edProvince">Province:</label>
                            <input type="text" id="editTech4edProvince" name="province">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edDistrict">District:</label>
                            <input type="text" id="editTech4edDistrict" name="district">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edMunicipality">Municipality:</label>
                            <input type="text" id="editTech4edMunicipality" name="municipality">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edBarangay">Barangay:</label>
                            <input type="text" id="editTech4edBarangay" name="barangay">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edStreet">Street:</label>
                            <input type="text" id="editTech4edStreet" name="street">
                        </div>
                        <div class="form-field half-width">
                            <label for="editTech4edLocation">Location:</label>
                            <input type="text" id="editTech4edLocation" name="location">
                        </div>
                        <!-- Center Information -->
                        <div class="form-field half-width">
                            <label for="editTech4edCname">Center Name:</label>
                            <input type="text" id="editTech4edCname" name="cname">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edHost">Host:</label>
                            <input type="text" id="editTech4edHost" name="host">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edCategory">Category:</label>
                            <input type="text" id="editTech4edCategory" name="category">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edLongitude">Longitude:</label>
                            <input type="text" id="editTech4edLongitude" name="longitude">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edLatitude">Latitude:</label>
                            <input type="text" id="editTech4edLatitude" name="latitude">
                        </div>
                        <!-- Center Manager Information -->
                        <div class="form-field half-width">
                            <label for="editTech4edCmanager">Center Manager:</label>
                            <input type="text" id="editTech4edCmanager" name="cmanager">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edCemail">CM Email:</label>
                            <input type="email" id="editTech4edCemail" name="cemail">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edCmobile">CM Mobile:</label>
                            <input type="text" id="editTech4edCmobile" name="cmobile">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edClandline">CM Landline:</label>
                            <input type="text" id="editTech4edClandline" name="clandline">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edCgender">CM Gender:</label>
                            <select id="editTech4edCgender" name="cgender">
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>
                        <!-- Assistant Manager Information -->
                        <div class="form-field half-width">
                            <label for="editTech4edAmanager">Assistant Manager:</label>
                            <input type="text" id="editTech4edAmanager" name="amanager">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edAemail">AM Email:</label>
                            <input type="email" id="editTech4edAemail" name="aemail">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edAmobile">AM Mobile:</label>
                            <input type="text" id="editTech4edAmobile" name="amobile">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edAlandline">AM Landline:</label>
                            <input type="text" id="editTech4edAlandline" name="alandline">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edAgender">AM Gender:</label>
                            <select id="editTech4edAgender" name="agender">
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>
                        <!-- Dates -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edLaunch">Launch Date:</label>
                            <input type="date" id="editTech4edLaunch" name="launch">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edRegistration">Registration Date:</label>
                            <input type="date" id="editTech4edRegistration" name="registration">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edOperation">Operation Date:</label>
                            <input type="date" id="editTech4edOperation" name="operation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edVisited">Last Visited:</label>
                            <input type="date" id="editTech4edVisited" name="visited">
                        </div>
                        <!-- Equipment -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edDesktop">Desktop:</label>
                            <input type="number" id="editTech4edDesktop" name="desktop" min="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edLaptop">Laptop:</label>
                            <input type="number" id="editTech4edLaptop" name="laptop" min="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edPrinter">Printer:</label>
                            <input type="number" id="editTech4edPrinter" name="printer" min="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edScanner">Scanner:</label>
                            <input type="number" id="editTech4edScanner" name="scanner" min="0">
                        </div>
                        <!-- Status and Connectivity -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edStatus">Status:</label>
                            <input type="text" id="editTech4edStatus" name="status">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edNetwork">Network:</label>
                            <input type="text" id="editTech4edNetwork" name="network">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edConnectivity">Connectivity:</label>
                            <input type="text" id="editTech4edConnectivity" name="connectivity">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edSpeed">Speed:</label>
                            <input type="text" id="editTech4edSpeed" name="speed">
                        </div>
                        <!-- Training Information -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edCmtmale">CM Male Trainees:</label>
                            <input type="number" id="editTech4edCmtmale" name="cmtmale" min="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edCmtfemale">CM Female Trainees:</label>
                            <input type="number" id="editTech4edCmtfemale" name="cmtfemale" min="0">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edStraining">S-Training:</label>
                            <input type="text" id="editTech4edStraining" name="straining">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edEtraining">E-Training:</label>
                            <input type="text" id="editTech4edEtraining" name="etraining">
                        </div>
                        <!-- Additional Information -->
                        <div class="form-field quarter-width">
                            <label for="editTech4edSigning">Signing:</label>
                            <input type="text" id="editTech4edSigning" name="signing">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edPartner">Partner:</label>
                            <input type="text" id="editTech4edPartner" name="partner">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edExpiration">Expiration:</label>
                            <input type="date" id="editTech4edExpiration" name="expiration">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edDonation">Donation:</label>
                            <input type="text" id="editTech4edDonation" name="donation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edDatedonation">Donation Date:</label>
                            <input type="date" id="editTech4edDatedonation" name="datedonation">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edTcms">TCMS:</label>
                            <input type="text" id="editTech4edTcms" name="tcms">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edKeyOne">Key One:</label>
                            <input type="text" id="editTech4edKeyOne" name="key_one">
                        </div>
                        <div class="form-field quarter-width">
                            <label for="editTech4edIdentifier">Identifier:</label>
                            <input type="text" id="editTech4edIdentifier" name="identifier">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="editTech4edModal">Cancel</button>
                <button type="submit" form="editTech4edForm" class="btn btn-primary" id="saveEditTech4edButton">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Manage Targets Modal -->
    <div id="manageTargetsModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2><i class="fas fa-bullseye"></i> Manage <?php echo htmlspecialchars($target_category_filter); ?> Targets</h2> <!-- <-- Changed Title -->
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <!-- Existing Targets Table -->
                <h4>Existing Targets</h4>
                 <div class="loading-indicator" id="targetsLoadingIndicator" style="display: none; padding: 20px;"> <i class="fas fa-spinner fa-spin"></i> Loading targets... </div>
                 <div class="error-indicator" id="targetsErrorIndicator" style="display: none; margin-bottom: 15px;"> Error loading targets. </div>
                 <div id="existingTargetsTableContainer">
                    <table id="existingTargetsTable">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Subcategory</th>
                                <th>Indicator</th>
                                <th>Year</th>
                                <th>Target Activities</th>
                                <th>Target Participants</th>
                                <th class="actions-col">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="targetsTableBody">
                            <!-- Target rows will be inserted here by JavaScript -->
                             <tr><td colspan="7" class="table-message">No targets found or loaded yet.</td></tr>
                        </tbody>
                    </table>
                 </div>

                <!-- Add/Edit Target Form -->
                <div id="addTargetFormContainer">
                    <h4 id="targetFormTitle">Add New Target</h4>
                    <form id="addTargetForm">
                        <input type="hidden" id="targetEditId" name="id" value="">
                        <div class="form-grid">
                            <div class="form-field category-field">
                                <label for="targetCategory">Category <span class="required">*</span></label>
                                <input type="text" id="targetCategory" name="category" value="<?php echo htmlspecialchars($target_category_filter); ?>" readonly> <!-- <-- Changed Default -->
                            </div>
                            <div class="form-field subcategory-field">
                                <label for="targetSubcategory">Subcategory <span class="required">*</span></label>
                                <input type="text" id="targetSubcategory" name="subcategory" placeholder="e.g., Capacity Development" required>
                            </div>
                             <div class="form-field indicator-field">
                                <label for="targetIndicator">Indicator <span class="required">*</span></label>
                                <input type="text" id="targetIndicator" name="indicator" placeholder="Indicator name from Activity table" required>
                                <small>Must match 'Indicator' in Activities table.</small>
                            </div>
                            <div class="form-field year-field">
                                <label for="targetYear">Year <span class="required">*</span></label>
                                <input type="number" id="targetYear" name="year" min="2000" max="2100" step="1" value="<?php echo htmlspecialchars($selected_report_year); ?>" required>
                            </div>
                            <div class="form-field target-field">
                                <label for="targetValue">Target Activities <span class="required">*</span></label>
                                <input type="number" id="targetValue" name="target" min="0" step="1" placeholder="e.g., 50" required>
                            </div>
                            <div class="form-field target-field">
                                <label for="targetParticipantsValue">Target Participants</label>
                                <input type="number" id="targetParticipantsValue" name="target_participants" min="0" step="1" placeholder="e.g., 100">
                                <small>Optional. Leave empty if not tracking participants.</small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer target-modal-footer">
                <div class="left-buttons">
                     <button type="button" class="btn btn-secondary" id="resetTargetFormButton">
                        <i class="fas fa-undo"></i> Reset Form
                    </button>
                </div>
                <div class="right-buttons">
                     <button type="button" class="btn btn-secondary close-modal" data-modal-id="manageTargetsModal">Close</button>
                    <button type="submit" form="addTargetForm" class="btn btn-primary" id="saveTargetButton">
                        <i class="fas fa-save"></i> Save Target
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- AJAX Endpoint URL -->
    <script> const ajaxHandlerUrl = 'ajax_handler.php'; </script>
    <!-- Core JavaScript -->
    <script>
       document.addEventListener('DOMContentLoaded', () => {
         console.log("Free Wifi for All Dashboard loaded. Initializing UI..."); // <-- Changed Log
         // --- Element References ---
         const selectAllCheckboxActivity = document.getElementById('selectAllCheckboxActivity');
         const selectAllCheckboxParticipant = document.getElementById('selectAllCheckboxParticipant');
         const activityRowCheckboxes = document.querySelectorAll('.activity-row-checkbox');
         const participantRowCheckboxes = document.querySelectorAll('.participant-row-checkbox');
         const selectionActionBar = document.getElementById('selectionActionBar');
         const selectedCountSpan = document.getElementById('selectedCount');
         const editButton = document.getElementById('editButton');
         const deleteButton = document.getElementById('deleteButton'); // Main delete button
         const uploadButton = document.getElementById('uploadButton');
         const viewFilesButton = document.getElementById('viewFilesButton');
         const closeActionBarButton = selectionActionBar?.querySelector('.close-action-bar');
         const addActivityButton = document.getElementById('addActivityButton');
         const addParticipantButton = document.getElementById('addParticipantButton');
         const notificationElement = document.getElementById('notification');
         const notificationMessageElement = document.getElementById('notificationMessage');
         const tabs = document.querySelectorAll('.nav-tab');
         const tabContents = document.querySelectorAll('.tab-content');
         const exportButton = document.getElementById('exportButton'); // Might be null if tab is reports
         const importButton = document.getElementById('importButton'); // Might be null if tab is reports
         const manageTargetsButton = document.getElementById('manageTargetsButton');
         const reportGraphContainer = document.getElementById('reportGraphContainer');
         const reportLoadingIndicator = document.getElementById('reportLoadingIndicator');
         const reportYearFilter = document.getElementById('reportYearFilter'); // NEW Year Filter

         // --- Modal References ---
         const modals = {
             addActivity: document.getElementById('addModal'),
             editActivity: document.getElementById('editModal'),
             addParticipant: document.getElementById('addParticipantModal'),
             editParticipant: document.getElementById('editParticipantModal'),
             addTech4ed: document.getElementById('addTech4edModal'),
             editTech4ed: document.getElementById('editTech4edModal'),
             upload: document.getElementById('uploadModal'),
             view: document.getElementById('viewFilesModal'),
             delete: document.getElementById('deleteModal'),
             import: document.getElementById('importModal'),
             export: document.getElementById('exportModal'),
             manageTargets: document.getElementById('manageTargetsModal'),
             statsBreakdown: document.getElementById('statsBreakdownModal')
         };
         // --- Manage Targets Modal Elements ---
         const targetsLoadingIndicator = document.getElementById('targetsLoadingIndicator');
         const targetsErrorIndicator = document.getElementById('targetsErrorIndicator');
         const targetsTableBody = document.getElementById('targetsTableBody');
         const targetForm = document.getElementById('addTargetForm');
         const targetFormTitle = document.getElementById('targetFormTitle');
         const targetEditIdInput = document.getElementById('targetEditId');
         const resetTargetFormButton = document.getElementById('resetTargetFormButton');
         const saveTargetButton = document.getElementById('saveTargetButton');
         const targetYearSelect = document.getElementById('targetYear'); // Reference to year input in target modal

         // Debug: Check if target elements are found
         console.log('Target modal elements found:');
         console.log('- targetsLoadingIndicator:', !!targetsLoadingIndicator);
         console.log('- targetsErrorIndicator:', !!targetsErrorIndicator);
         console.log('- targetsTableBody:', !!targetsTableBody);
         console.log('- targetForm:', !!targetForm);
         console.log('- saveTargetButton:', !!saveTargetButton);

         // --- Upload Modal Elements ---
         const uploadModalTitle = document.getElementById('uploadModalTitle'); const uploadItemCount = document.getElementById('uploadItemCount'); const uploadItemType = document.getElementById('uploadItemType'); const uploadForm = document.getElementById('uploadForm'); const submitUploadButton = document.getElementById('submitUploadButton'); const fileInput = document.getElementById('fileInput'); const uploadTypeInfo = document.getElementById('uploadTypeInfo');
         // --- View Files Modal Elements ---
         const viewFilesModal = document.getElementById('viewFilesModal'); const viewFilesModalTitle = document.getElementById('viewFilesModalTitle'); const viewFilesParentItemInfo = document.getElementById('viewFilesParentItemInfo'); const viewFilesSelectAll = document.getElementById('viewFilesSelectAll'); const viewFilesDeleteSelected = document.getElementById('viewFilesDeleteSelected'); const fileListContainer = document.getElementById('fileListContainer'); const fileGroupContainer = document.getElementById('fileGroupContainer'); const viewLoadingIndicator = document.getElementById('viewLoadingIndicator'); const viewFilesEmptyMessage = document.getElementById('viewFilesEmptyMessage'); const viewFilesErrorMessage = document.getElementById('viewFilesErrorMessage');
         // --- Delete Confirmation Modal Elements ---
         const deleteModal = document.getElementById('deleteModal'); const deleteModalTitle = document.getElementById('deleteModalTitle'); const deleteItemTypeSpan = document.getElementById('deleteItemType'); const deleteItemCountSpan = document.getElementById('deleteItemCount'); const deleteFileWarning = document.getElementById('deleteFileWarning'); const deleteItemWarning = document.getElementById('deleteItemWarning'); const confirmDeleteButton = document.getElementById('confirmDeleteButton');
         // --- Export Modal Elements ---
         const exportModal = document.getElementById('exportModal');
         const exportModalTitle = document.getElementById('exportModalTitle');
         const exportAllLabel = document.getElementById('exportAllLabel');
         const exportFilteredLabel = document.getElementById('exportFilteredLabel');
         const exportSelectedLabel = document.getElementById('exportSelectedLabel');
         const filteredItemsCount = document.getElementById('filteredItemsCount');
         const selectedItemsCount = document.getElementById('selectedItemsCount');
         const exportProcessingIndicator = document.getElementById('exportProcessingIndicator');
         const exportErrorIndicator = document.getElementById('exportErrorIndicator');
         const exportDataButton = document.getElementById('exportDataButton');
         // --- Import Modal Elements ---
         const importModal = document.getElementById('importModal');
         const importModalTitle = document.getElementById('importModalTitle');
         const importInstructions = document.getElementById('importInstructions');
         const importRequiredColumnsInfo = document.getElementById('importRequiredColumnsInfo');
         const importRequiredColumnsList = document.getElementById('importRequiredColumnsList');
         const csvFileInput = document.getElementById('csvFileInput');
         const fileNameDisplay = document.getElementById('fileNameDisplay');
         const previewDataButton = document.getElementById('previewDataButton');
         const importDataButton = document.getElementById('importDataButton');
         const previewTableContainer = document.getElementById('previewTableContainer');
         const previewTable = document.getElementById('previewTable');
         const previewTableHead = previewTable?.querySelector('thead');
         const previewTableBody = previewTable?.querySelector('tbody');
         const previewRowCount = document.getElementById('previewRowCount');
         const importStep1 = document.getElementById('importStep1');
         const importStep2 = document.getElementById('importStep2');
         const importErrorStep1 = document.getElementById('importErrorStep1');
         const importErrorStep2 = document.getElementById('importErrorStep2');
         const previewLoadingIndicator = document.getElementById('previewLoadingIndicator');
         const importProcessingIndicator = document.getElementById('importProcessingIndicator');
         const importResultIndicator = document.getElementById('importResultIndicator');

         // --- Import State ---
         let selectedFile = null; let parsedDataForImport = null;
         let currentImportType = 'activity'; // Default, will be set when modal opens
         const requiredColumnsActivity = [ 'Start Date', 'End Date', 'Bureau', 'Project', 'Activity Name', 'Indicator', 'Training Venue', 'Municipality/City', 'District', 'Barangay', 'Requesting Agency', 'Mode of Implementation', 'Target Sector', 'Responsible Person', 'Resource Person', 'Participants', 'Completers', 'Male', 'Female', 'Approved Activity Design', 'Link to MOVs', 'Remarks' ]; // Note: 'Bureau' maps to 'project' in DB, 'Project' maps to 'subproject'
         const requiredColumnsParticipant = [ 'Start Date', 'End Date', 'Activity', 'Indicator', 'Full Name', 'Sex', 'Contact', 'Email', 'Mode', 'Agency', 'Sector', 'Responsible Person', 'Remarks' ]; // 'Project' is handled automatically

         // --- Charting State ---
         let reportCharts = {}; // To store Chart.js instances

         // --- State Variables ---
         let notificationTimeout;
         let currentActiveTab = '<?php echo $active_tab; ?>'; // Use PHP value
         let currentViewFilesType = 'activity';
         let deleteContext = { type: null, ids: [] };
         let currentReportYear = '<?php echo $selected_report_year; ?>'; // Use PHP value

         // Tech4ED variables
         let tech4edRowCheckboxes = document.querySelectorAll('.tech4ed-row-checkbox');
         let selectAllCheckboxTech4ed = document.getElementById('selectAllCheckboxTech4ed');

         // --- Utility Functions ---
         function showNotification(message, type = 'success', duration = 3000) { if (!notificationElement || !notificationMessageElement) return; clearTimeout(notificationTimeout); notificationMessageElement.textContent = message; notificationElement.className = 'notification ' + type; requestAnimationFrame(() => { notificationElement.classList.add('visible'); }); notificationTimeout = setTimeout(() => { notificationElement.classList.remove('visible'); }, duration); }
         function getSelectedIds(type = 'activity') {
             let selector;
             if (type === 'activity') {
                 selector = '.activity-row-checkbox:checked';
             } else if (type === 'participant') {
                 selector = '.participant-row-checkbox:checked';
             } else if (type === 'tech4ed') {
                 selector = '.tech4ed-row-checkbox:checked';
             } else {
                 selector = '.row-checkbox:checked';
             }
             return Array.from(document.querySelectorAll(selector)).map(cb => cb.value);
         }
         function updateActionBar() {
             const activeType = currentActiveTab === 'participants' ? 'participant'
                              : currentActiveTab === 'tech4ed' ? 'tech4ed'
                              : 'activity';
             const selectedIds = getSelectedIds(activeType);
             const count = selectedIds.length;

             if (selectedCountSpan) selectedCountSpan.textContent = `${count} ${activeType}${count !== 1 ? 's' : ''} selected`;
             if (selectionActionBar) selectionActionBar.classList.toggle('visible', count > 0);
             if (editButton) editButton.disabled = count !== 1;
             if (deleteButton) deleteButton.disabled = count === 0;
             if (uploadButton) uploadButton.disabled = count === 0;
             if (viewFilesButton) viewFilesButton.disabled = count === 0;

             // Determine which checkbox and rows to use based on active tab
             let selectAllCheckbox, rowCheckboxes;
             if (activeType === 'activity') {
                 selectAllCheckbox = selectAllCheckboxActivity;
                 rowCheckboxes = activityRowCheckboxes;
             } else if (activeType === 'participant') {
                 selectAllCheckbox = selectAllCheckboxParticipant;
                 rowCheckboxes = participantRowCheckboxes;
             } else if (activeType === 'tech4ed') {
                 selectAllCheckbox = selectAllCheckboxTech4ed;
                 rowCheckboxes = tech4edRowCheckboxes;
             }

             const totalCheckboxes = rowCheckboxes.length;
             if (selectAllCheckbox) {
                 if (totalCheckboxes > 0) {
                     selectAllCheckbox.checked = (count === totalCheckboxes);
                     selectAllCheckbox.indeterminate = (count > 0 && count < totalCheckboxes);
                 } else {
                     selectAllCheckbox.checked = false;
                     selectAllCheckbox.indeterminate = false;
                 }
             }

             // Reset other checkboxes
             if (activeType !== 'activity' && selectAllCheckboxActivity) {
                 selectAllCheckboxActivity.checked = false;
                 selectAllCheckboxActivity.indeterminate = false;
             }
             if (activeType !== 'participant' && selectAllCheckboxParticipant) {
                 selectAllCheckboxParticipant.checked = false;
                 selectAllCheckboxParticipant.indeterminate = false;
             }
             if (activeType !== 'tech4ed' && selectAllCheckboxTech4ed) {
                 selectAllCheckboxTech4ed.checked = false;
                 selectAllCheckboxTech4ed.indeterminate = false;
             }
         }
         async function performAjax(action, data = {}) {
            const formData = new FormData();
            formData.append('action', action);

            for (const key in data) {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    const value = data[key];
                    if (Array.isArray(value)) {
                        if (key === 'activities' || key === 'participants' || key === 'tech4ed_items') {
                             formData.append(key, JSON.stringify(value));
                         } else {
                            value.forEach(item => formData.append(key + '[]', item));
                        }
                    } else if (value instanceof FileList) {
                        for (let i = 0; i < value.length; i++) {
                            formData.append('files[]', value[i]);
                        }
                    } else if (value !== null && value !== undefined) {
                        formData.append(key, value);
                    }
                }
            }

            try {
                const response = await fetch(ajaxHandlerUrl, { method: 'POST', body: formData });
                if (!response.ok) { throw new Error(`HTTP error! Status: ${response.status}`); }
                const responseClone = response.clone();
                let responseData;
                try { responseData = await response.json(); }
                catch (jsonError) { const errorText = await responseClone.text(); console.error('Non-JSON response:', errorText); throw new Error(`Server returned non-JSON response (Status: ${response.status}).`); }
                return responseData;
            } catch (error) {
                console.error('AJAX Error:', error);
                let userMessage = 'An unexpected error occurred.';
                if (error.message.includes('non-JSON')) userMessage = 'An error occurred communicating with the server.';
                else if (error.message.includes('HTTP error')) userMessage = `A server error occurred (${error.message}).`;
                else userMessage = `Error: ${error.message}.`;
                showNotification(userMessage, 'error', 5000);
                return { success: false, message: error.message, data: null };
            }
        }
         function escapeHtml(unsafe) {
             if (unsafe === null || typeof unsafe === 'undefined') return '';
             // Convert to string first, then apply replacements
             const text = String(unsafe);
             return text.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
         }
         function formatDateForInputJS(dateString) { if (!dateString || dateString === '0000-00-00' || dateString.startsWith('0000-00-00')) return ''; try { const datePart = dateString.split(' ')[0]; const date = new Date(datePart + 'T00:00:00'); if (isNaN(date.getTime())) { console.warn("Invalid date string:", dateString); return ''; } const year = date.getFullYear(); const month = (date.getMonth() + 1).toString().padStart(2, '0'); const day = date.getDate().toString().padStart(2, '0'); return `${year}-${month}-${day}`; } catch (e) { console.error("Error formatting date:", dateString, e); return ''; } }
         function getFileIconClass(filename) { let fileIcon = 'fa-file'; const ext = filename?.split('.').pop()?.toLowerCase(); if (['pdf'].includes(ext)) fileIcon = 'fa-file-pdf'; else if (['doc', 'docx'].includes(ext)) fileIcon = 'fa-file-word'; else if (['xls', 'xlsx'].includes(ext)) fileIcon = 'fa-file-excel'; else if (['png', 'jpg', 'jpeg', 'gif', 'bmp'].includes(ext)) fileIcon = 'fa-file-image'; else if (['zip', 'rar', '7z'].includes(ext)) fileIcon = 'fa-file-archive'; return fileIcon; }

         // --- Modal Handling ---
         function openModal(modalKey) {
             const modal = modals[modalKey];
             if (!modal) { console.error("Modal not found for key:", modalKey); return; }

             // Special handling for manageTargets modal needs to happen *before* generic reset/open
             if (modalKey === 'manageTargets') {
                openManageTargetsModal(); // Call specific function to load data first
             } else {
                 resetModalContent(modalKey); // Standard reset for others
             }

             const activeType = currentActiveTab === 'participants' ? 'participant'
                              : currentActiveTab === 'tech4ed' ? 'tech4ed' // Add tech4ed type
                              : 'activity';
             const selectedParentIds = getSelectedIds(activeType);
             const count = selectedParentIds.length;

             if (modalKey === 'export') updateExportModalContent(activeType);
             else if (modalKey === 'import') { currentImportType = activeType; updateImportModalContent(activeType); }

             switch (modalKey) {
                 case 'editActivity': case 'editParticipant': case 'editTech4ed':
                    if (count !== 1) { showNotification(`Please select exactly one ${activeType} to edit.`, "error"); return; }
                    let editLoadingIndicatorId, editFormId, editErrorIndicatorId, saveEditButtonId;

                    if (activeType === 'activity') {
                        editLoadingIndicatorId = 'editActivityLoadingIndicator';
                        editFormId = 'editActivityForm';
                        editErrorIndicatorId = 'editActivityErrorIndicator';
                        saveEditButtonId = 'saveEditActivityButton';
                    } else if (activeType === 'participant') {
                        editLoadingIndicatorId = 'editParticipantLoadingIndicator';
                        editFormId = 'editParticipantForm';
                        editErrorIndicatorId = 'editParticipantErrorIndicator';
                        saveEditButtonId = 'saveEditParticipantButton';
                    } else if (activeType === 'tech4ed') {
                        editLoadingIndicatorId = 'editTech4edLoadingIndicator';
                        editFormId = 'editTech4edForm';
                        editErrorIndicatorId = 'editTech4edErrorIndicator';
                        saveEditButtonId = 'saveEditTech4edButton';
                    }

                    modal.querySelector(`#${editLoadingIndicatorId}`).style.display = 'flex';
                    modal.querySelector(`#${editFormId}`).style.display = 'none';
                    modal.querySelector(`#${editErrorIndicatorId}`).style.display = 'none';
                    modal.querySelector(`#${saveEditButtonId}`).disabled = true;

                    if (activeType === 'activity') fetchActivityData(selectedParentIds[0]);
                    else if (activeType === 'participant') fetchParticipantData(selectedParentIds[0]);
                    else if (activeType === 'tech4ed') fetchTech4edData(selectedParentIds[0]);
                    break;
                 case 'delete':
                      if (deleteContext.type && deleteContext.ids && deleteContext.ids.length > 0) {
                         if (deleteModalTitle) deleteModalTitle.textContent = 'Confirm Deletion';
                         if (deleteItemTypeSpan) deleteItemTypeSpan.textContent = `${deleteContext.type}(s)`;
                         if (deleteItemCountSpan) deleteItemCountSpan.textContent = deleteContext.ids.length;
                         if (deleteFileWarning) deleteFileWarning.style.display = 'none';
                         if (deleteItemWarning) deleteItemWarning.style.display = 'block';
                     } else {
                         console.error("Delete modal open attempt with invalid context:", deleteContext);
                         showNotification("Error: Cannot determine items to delete. Please re-select.", "error");
                         return;
                     }
                     break;
                 case 'upload':
                     if (count === 0) { showNotification(`No ${activeType}s selected for file upload.`, "info"); return; }
                     uploadModalTitle.textContent = `Upload Files for ${activeType.charAt(0).toUpperCase() + activeType.slice(1)}s`;
                     uploadItemCount.textContent = count;
                     uploadItemType.textContent = `${activeType}(s)`;
                     if(fileInput) fileInput.value = '';
                     uploadTypeInfo.innerHTML = `<i class="fas fa-info-circle"></i> Files will be associated with the selected ${activeType}(s). ${activeType === 'participant' ? 'Participant files are stored separately.' : ''}`;
                     if(submitUploadButton) submitUploadButton.disabled = false;
                     break;
                 case 'view':
                     if (count === 0) { showNotification(`No ${activeType}s selected to view files.`, "info"); return; }
                     currentViewFilesType = activeType;
                     viewFilesModalTitle.textContent = `View Associated Files`;
                     viewFilesParentItemInfo.textContent = `Files for ${count} selected ${activeType}(s):`;
                     if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active');
                     if(fileGroupContainer) fileGroupContainer.innerHTML = '';
                     if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none';
                     if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none';
                     if(viewFilesSelectAll) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true; }
                     if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true;
                     if (activeType === 'activity') fetchActivityFiles(selectedParentIds);
                     else if (activeType === 'tech4ed') fetchTech4edFiles(selectedParentIds);
                     else fetchParticipantFiles(selectedParentIds);
                     break;
                 case 'addActivity':
                     modal.querySelector('#addActivityForm')?.reset();
                     modal.querySelector('#addActProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>';
                     modal.querySelector('#saveAddActivityButton').disabled = false;
                     modal.querySelector('#saveAddActivityButton').innerHTML = 'Save Activity';
                     break;
                 case 'addParticipant':
                     modal.querySelector('#addParticipantForm')?.reset();
                     modal.querySelector('#addPartProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>';
                     modal.querySelector('#saveAddParticipantButton').disabled = false;
                     modal.querySelector('#saveAddParticipantButton').innerHTML = 'Save Participant';
                     break;
                 case 'statDetail':
                     // Content is populated by the click listener before calling openModal
                     break;
                 case 'manageTargets':
                    // Data loading handled by openManageTargetsModal() called earlier
                    break;
             }
             modal.classList.add('visible');
             document.body.style.overflow = 'hidden';
         }
         function closeModal(modalKey) {
             const modal = modals[modalKey];
             if (modal) { modal.classList.remove('visible'); }
             if (modalKey === 'delete') { deleteContext = { type: null, ids: [] }; }
             if (modalKey === 'import') resetImportModal();
             else if (modalKey === 'export') resetExportModal();
             // ADD: Reset target form if closing manageTargets modal
             else if (modalKey === 'manageTargets') {
                 resetTargetForm();
             }
             const anyVisible = Object.values(modals).some(m => m?.classList.contains('visible'));
             if (!anyVisible) { document.body.style.overflow = ''; }
         }
         function resetModalContent(modalKey) {
             const modal = modals[modalKey];
             if (!modal) return;
             switch(modalKey) {
                 case 'editActivity': modal.querySelector('#editActivityForm')?.reset(); modal.querySelector('#editActivityModalTitle').textContent = 'Edit Activity'; modal.querySelector('#editActivityLoadingIndicator').style.display = 'block'; modal.querySelector('#editActivityErrorIndicator').style.display = 'none'; modal.querySelector('#editActivityForm').style.display = 'none'; modal.querySelector('#saveEditActivityButton').disabled = true; break;
                 case 'editParticipant': modal.querySelector('#editParticipantForm')?.reset(); modal.querySelector('#editParticipantModalTitle').textContent = 'Edit Participant'; modal.querySelector('#editParticipantLoadingIndicator').style.display = 'block'; modal.querySelector('#editParticipantErrorIndicator').style.display = 'none'; modal.querySelector('#editParticipantForm').style.display = 'none'; modal.querySelector('#saveEditParticipantButton').disabled = true; break;
                 case 'editTech4ed': modal.querySelector('#editTech4edForm')?.reset(); modal.querySelector('#editTech4edModalTitle').textContent = 'Edit Tech4ED DTC'; modal.querySelector('#editTech4edLoadingIndicator').style.display = 'block'; modal.querySelector('#editTech4edErrorIndicator').style.display = 'none'; modal.querySelector('#editTech4edForm').style.display = 'none'; modal.querySelector('#saveEditTech4edButton').disabled = true; break;
                 case 'delete': if(deleteModalTitle) deleteModalTitle.textContent = 'Confirm Deletion'; if(deleteItemTypeSpan) deleteItemTypeSpan.textContent = 'item(s)'; if(deleteItemCountSpan) deleteItemCountSpan.textContent = '0'; if(deleteFileWarning) deleteFileWarning.style.display = 'none'; if(deleteItemWarning) deleteItemWarning.style.display = 'block'; break;
                 case 'upload': modal.querySelector('#uploadForm')?.reset(); modal.querySelector('#uploadItemCount').textContent = '0'; modal.querySelector('#uploadItemType').textContent = 'item(s)'; break;
                 case 'view': if (viewLoadingIndicator) viewLoadingIndicator.classList.remove('active'); if (fileGroupContainer) fileGroupContainer.innerHTML = ''; if (viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if (viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none'; if (viewFilesSelectAll) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true; } if (viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; if (viewFilesParentItemInfo) viewFilesParentItemInfo.textContent = 'Files for 0 selected item(s):'; break;
                 case 'addActivity': modal.querySelector('#addActivityForm')?.reset(); modal.querySelector('#addActProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; modal.querySelector('#saveAddActivityButton').disabled = false; modal.querySelector('#saveAddActivityButton').innerHTML = 'Save Activity'; break;
                 case 'addParticipant': modal.querySelector('#addParticipantForm')?.reset(); modal.querySelector('#addPartProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; modal.querySelector('#saveAddParticipantButton').disabled = false; modal.querySelector('#saveAddParticipantButton').innerHTML = 'Save Participant'; break;
                 case 'addTech4ed': modal.querySelector('#addTech4edForm')?.reset(); modal.querySelector('#saveAddTech4edButton').disabled = false; modal.querySelector('#saveAddTech4edButton').innerHTML = '<i class="fas fa-save"></i> Save Tech4ED DTC'; break;
                 case 'import': resetImportModal(); break;
                 case 'export': resetExportModal(); break;
                 case 'manageTargets':
                    resetTargetForm(); // Ensure form is reset
                    if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="7" class="table-message">Loading targets...</td></tr>'; // Clear table visually
                    if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'none';
                    if(targetsErrorIndicator) targetsErrorIndicator.style.display = 'none';
                    break;
                 case 'statsBreakdown':
                    // Reset stats breakdown modal (matching index.php structure)
                    const loadingIndicator = modal.querySelector('#statsBreakdownLoadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                        loadingIndicator.classList.add('active');
                    }
                    modal.querySelector('#statsBreakdownErrorIndicator').style.display = 'none';
                    modal.querySelector('#statsBreakdownContent').style.display = 'none';
                    modal.querySelector('#statsModalTitle').textContent = 'Statistics Breakdown';

                    // Reset table
                    const tableBody = modal.querySelector('#statsBreakdownTable tbody');
                    if (tableBody) tableBody.innerHTML = '';

                    // Reset search
                    const searchInput = document.getElementById('statsTableSearch');
                    if (searchInput) {
                        searchInput.value = '';
                        searchInput.removeAttribute('data-search-setup');
                    }

                    // Reset pagination
                    const lengthSelect = document.getElementById('statsTableLength');
                    if (lengthSelect) {
                        lengthSelect.value = '10';
                        lengthSelect.removeAttribute('data-pagination-setup');
                    }

                    // Reset pagination buttons
                    const prevBtn = document.getElementById('statsTablePrevious');
                    if (prevBtn) prevBtn.removeAttribute('data-pagination-setup');

                    const nextBtn = document.getElementById('statsTableNext');
                    if (nextBtn) nextBtn.removeAttribute('data-pagination-setup');

                    // Reset pagination state
                    rowsPerPage = 10;
                    currentPage = 1;

                    // Reset data arrays
                    currentStatData = [];
                    filteredData = [];

                    // Clear table
                    if (tableBody) tableBody.innerHTML = '';

                    // Reset pagination display
                    const pageNumbersContainer = document.getElementById('statsTablePageNumbers');
                    if (pageNumbersContainer) pageNumbersContainer.innerHTML = '';

                    // Reset pagination info
                    const startEl = document.getElementById('statsTableStart');
                    const endEl = document.getElementById('statsTableEnd');
                    const totalEl = document.getElementById('statsTableTotal');
                    if (startEl) startEl.textContent = '1';
                    if (endEl) endEl.textContent = '10';
                    if (totalEl) totalEl.textContent = '0';
                    break;
             }
         }
         // --- Reset Functions for Specific Modals ---
         function resetExportModal() { if (!exportModal) return; const allRadio = exportModal.querySelector('input[name="exportType"][value="all"]'); if (allRadio) allRadio.checked = true; const csvRadio = exportModal.querySelector('input[name="exportFormat"][value="csv"]'); if (csvRadio) csvRadio.checked = true; updateExportModalContent(currentActiveTab === 'participants' ? 'participant' : 'activity'); if (exportProcessingIndicator) exportProcessingIndicator.style.display = 'none'; if (exportErrorIndicator) { exportErrorIndicator.style.display = 'none'; exportErrorIndicator.textContent = ''; } if (exportDataButton) exportDataButton.disabled = false; }
         function resetImportModal() { if (!importModal) return; selectedFile = null; parsedDataForImport = null; if(csvFileInput) csvFileInput.value = ''; if(fileNameDisplay) fileNameDisplay.textContent = 'No file chosen'; if(importStep1) importStep1.style.display = 'block'; if(importStep2) importStep2.style.display = 'none'; if(previewTableContainer) previewTableContainer.style.display = 'none'; if(previewTableHead) previewTableHead.innerHTML = ''; if(previewTableBody) previewTableBody.innerHTML = ''; if(previewRowCount) previewRowCount.textContent = ''; if(previewDataButton) previewDataButton.disabled = true; if(importDataButton) importDataButton.disabled = true; if(importErrorStep1) importErrorStep1.style.display = 'none'; if(importErrorStep2) importErrorStep2.style.display = 'none'; if(previewLoadingIndicator) previewLoadingIndicator.style.display = 'none'; if(importProcessingIndicator) importProcessingIndicator.style.display = 'none'; if(importResultIndicator) { importResultIndicator.style.display = 'none'; importResultIndicator.className = ''; importResultIndicator.textContent = ''; } updateImportModalContent(currentActiveTab === 'participants' ? 'participant' : 'activity'); }
         function resetTargetForm() {
            if (targetForm) targetForm.reset();
            if (targetEditIdInput) targetEditIdInput.value = '';
            if (targetFormTitle) targetFormTitle.textContent = 'Add New Target';
            if (saveTargetButton) {
                saveTargetButton.disabled = false;
                saveTargetButton.innerHTML = '<i class="fas fa-save"></i> Save Target';
            }
             // Set category back to default readonly value if form reset clears it
             const categoryInput = document.getElementById('targetCategory');
             if(categoryInput) categoryInput.value = '<?php echo htmlspecialchars($target_category_filter, ENT_QUOTES); ?>'; // <-- Use correct variable
             // Set default year using the JS variable tracking the report filter
             if(targetYearSelect) targetYearSelect.value = currentReportYear;
             // Clear the target participants field
             const targetParticipantsInput = document.getElementById('targetParticipantsValue');
             if(targetParticipantsInput) targetParticipantsInput.value = '';
         }


         function updateExportModalContent(activeType) {
             const typeName = activeType.charAt(0).toUpperCase() + activeType.slice(1);
             const typeNamePlural = typeName + 's';

             if (exportModalTitle) exportModalTitle.innerHTML = `<i class="fas fa-file-export"></i> Export ${typeNamePlural}`;
             if (exportAllLabel) exportAllLabel.textContent = `All ${typeNamePlural.toLowerCase()}`;
             if (exportFilteredLabel) exportFilteredLabel.textContent = `Filtered ${typeNamePlural.toLowerCase()}`;
             if (exportSelectedLabel) exportSelectedLabel.textContent = `Selected ${typeNamePlural.toLowerCase()}`;

             let totalFiltered = 0;
             if (activeType === 'activity') {
                 totalFiltered = <?php echo $total_activities; ?>;
             } else if (activeType === 'tech4ed') {
                 totalFiltered = <?php echo $total_tech4ed ?? 0; ?>;
             } else {
                 totalFiltered = <?php echo $total_participants; ?>;
             }

             if (filteredItemsCount) {
                 filteredItemsCount.textContent = `${totalFiltered} ${typeNamePlural.toLowerCase()} in current filter`;
             }

             const selectedIds = getSelectedIds(activeType);
             const selectedCount = selectedIds.length;

             if (selectedItemsCount) {
                 selectedItemsCount.textContent = `${selectedCount} ${typeNamePlural.toLowerCase()} selected`;
             }

             const selectedRadio = exportModal?.querySelector('input[name="exportType"][value="selected"]');
             const allRadio = exportModal?.querySelector('input[name="exportType"][value="all"]');

             if (selectedRadio) {
                 selectedRadio.disabled = (selectedCount === 0);
                 if (selectedCount === 0 && selectedRadio.checked && allRadio) {
                     allRadio.checked = true;
                 }
             }
         }
         function updateImportModalContent(activeType) {
             const typeName = activeType.charAt(0).toUpperCase() + activeType.slice(1);
             const typeNamePlural = typeName + 's';
             let requiredCols;

             if (activeType === 'activity') {
                 requiredCols = requiredColumnsActivity;
             } else if (activeType === 'tech4ed') {
                 requiredCols = [
                     'Region', 'Province', 'District', 'Municipality', 'Barangay',
                     'Street', 'Location', 'Center Name', 'Host', 'Category',
                     'Longitude', 'Latitude', 'Center Manager', 'CM Email',
                     'CM Mobile', 'CM Landline', 'CM Gender', 'Asst. Manager',
                     'AM Email', 'AM Mobile', 'AM Landline', 'AM Gender',
                     'Launch Date', 'Registration Date', 'Operation Date',
                     'Last Visited', 'Desktop', 'Laptop', 'Printer', 'Scanner',
                     'Status', 'Network', 'Connectivity', 'Speed',
                     'CM Male Trainees', 'CM Female Trainees', 'S-Training',
                     'E-Training', 'Signing', 'Partner', 'Expiration',
                     'Donation', 'Donation Date', 'TCMS', 'Key One', 'Identifier'
                 ];
             } else {
                 requiredCols = requiredColumnsParticipant;
             }

             if (importModalTitle) importModalTitle.innerHTML = `<i class="fas fa-file-import"></i> Import ${typeNamePlural}`;
             if (importInstructions) importInstructions.textContent = `Select a CSV file containing the ${typeNamePlural.toLowerCase()} to import.`;
             if (importRequiredColumnsList) {
                 importRequiredColumnsList.innerHTML = '';
                 requiredCols.forEach(col => {
                     const span = document.createElement('span');
                     span.className = 'tag';
                     span.textContent = col;
                     importRequiredColumnsList.appendChild(span);
                 });

                 // Add note for Tech4ED DTCs
                 const noteElement = document.getElementById('importNote');
                 if (noteElement) {
                     if (activeType === 'tech4ed') {
                         noteElement.innerHTML = '<i class="fas fa-info-circle"></i> Note: Only "Center Name" is required, but including all columns is recommended for complete data.';
                         noteElement.style.display = 'block';
                     } else if (activeType === 'activity') {
                         noteElement.innerHTML = '<i class="fas fa-info-circle"></i> Note: For Activities, the \'Bureau\' column must match \'ILCDB\' and will be validated during import.';
                         noteElement.style.display = 'block';
                     } else {
                         noteElement.style.display = 'none';
                     }
                 }
             }
         }
         function showImportError(message, step = 1) { const errorDiv = step === 1 ? importErrorStep1 : importErrorStep2; if (errorDiv) { errorDiv.textContent = message; errorDiv.style.display = 'block'; errorDiv.className = 'error-indicator'; } if (importResultIndicator) importResultIndicator.style.display = 'none'; }
         function showImportMessage(message, step = 2) { const errorDiv = step === 1 ? importErrorStep1 : importErrorStep2; const resultDiv = importResultIndicator; if (errorDiv) { errorDiv.textContent = message; errorDiv.style.display = 'block'; errorDiv.className = 'info-indicator'; } if(resultDiv) resultDiv.style.display = 'none'; }
         function displayPreviewTable(headers, data) { if (!previewTableHead || !previewTableBody || !previewTableContainer) return; previewTableHead.innerHTML = ''; previewTableBody.innerHTML = ''; const headerRow = previewTableHead.insertRow(); headers.forEach(headerText => { const th = document.createElement('th'); th.textContent = headerText; headerRow.appendChild(th); }); const previewLimit = 10; const dataToPreview = data.slice(0, previewLimit); dataToPreview.forEach(rowData => { const row = previewTableBody.insertRow(); headers.forEach(header => { const cell = row.insertCell(); const cellValue = rowData[header] !== null && rowData[header] !== undefined ? String(rowData[header]) : ''; cell.textContent = cellValue; cell.title = cellValue; }); }); previewTableContainer.style.display = 'block'; if (previewRowCount) { previewRowCount.textContent = `Showing first ${dataToPreview.length} of ${data.length} data rows.`; } }

         // --- Data Fetching Functions ---
         async function fetchActivityData(id) { console.log(`Fetching activity data for ID: ${id}`); const modal = modals.editActivity; const form = modal.querySelector('#editActivityForm'); const loadingIndicator = modal.querySelector('#editActivityLoadingIndicator'); const errorIndicator = modal.querySelector('#editActivityErrorIndicator'); const saveButton = modal.querySelector('#saveEditActivityButton'); loadingIndicator.style.display = 'flex'; errorIndicator.style.display = 'none'; form.style.display = 'none'; form.reset(); saveButton.disabled = true; const result = await performAjax('getActivity', { id: id }); loadingIndicator.style.display = 'none'; if (result?.success && result.data) { const data = result.data; try { form.querySelector('#editActivityFormId').value = data.id || ''; form.querySelector('#editActStart').value = formatDateForInputJS(data.start); form.querySelector('#editActEnd').value = formatDateForInputJS(data.end); form.querySelector('#editActProject').value = data.project ?? ''; form.querySelector('#editActSubproject').value = data.subproject ?? ''; form.querySelector('#editActActivityName').value = data.activity ?? ''; form.querySelector('#editActIndicator').value = data.indicator ?? ''; form.querySelector('#editActTraining').value = data.training ?? ''; form.querySelector('#editActMunicipality').value = data.municipality ?? ''; form.querySelector('#editActDistrict').value = data.district ?? ''; form.querySelector('#editActBarangay').value = data.barangay ?? ''; form.querySelector('#editActAgency').value = data.agency ?? ''; form.querySelector('#editActMode').value = data.mode ?? ''; form.querySelector('#editActSector').value = data.sector ?? ''; form.querySelector('#editActPerson').value = data.person ?? ''; form.querySelector('#editActResource').value = data.resource ?? ''; form.querySelector('#editActParticipants').value = data.participants ?? '0'; form.querySelector('#editActCompleters').value = data.completers ?? '0'; form.querySelector('#editActMale').value = data.male ?? '0'; form.querySelector('#editActFemale').value = data.female ?? '0'; form.querySelector('#editActApproved').value = data.approved ?? ''; form.querySelector('#editActMov').value = data.mov ?? ''; form.querySelector('#editActRemarks').value = data.remarks ?? ''; modal.querySelector('#editActivityModalTitle').textContent = 'Edit Activity'; form.style.display = 'grid'; saveButton.disabled = false; } catch (populateError) { console.error("Error populating activity edit form:", populateError); errorIndicator.textContent = `Error displaying data: ${populateError.message}.`; errorIndicator.style.display = 'block'; } } else { errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve activity data.'}`; errorIndicator.style.display = 'block'; } }
         async function fetchParticipantData(id) { console.log(`Fetching participant data for ID: ${id}`); const modal = modals.editParticipant; const form = modal.querySelector('#editParticipantForm'); const loadingIndicator = modal.querySelector('#editParticipantLoadingIndicator'); const errorIndicator = modal.querySelector('#editParticipantErrorIndicator'); const saveButton = modal.querySelector('#saveEditParticipantButton'); loadingIndicator.style.display = 'flex'; errorIndicator.style.display = 'none'; form.style.display = 'none'; form.reset(); saveButton.disabled = true; const result = await performAjax('getParticipant', { id: id }); loadingIndicator.style.display = 'none'; if (result?.success && result.data) { const data = result.data; try { form.querySelector('#editParticipantFormId').value = data.id || ''; form.querySelector('#editPartStart').value = formatDateForInputJS(data.start); form.querySelector('#editPartEnd').value = formatDateForInputJS(data.end); form.querySelector('#editPartActivity').value = data.activity ?? ''; form.querySelector('#editPartIndicator').value = data.indicator ?? ''; form.querySelector('#editPartFullname').value = data.fullname ?? ''; const sexSelect = form.querySelector('#editPartSex'); const sexValue = data.sex ?? ''; Array.from(sexSelect.options).forEach(option => { option.selected = (option.value === sexValue); }); form.querySelector('#editPartContact').value = data.contact ?? ''; form.querySelector('#editPartEmail').value = data.email ?? ''; form.querySelector('#editPartMode').value = data.mode ?? ''; form.querySelector('#editPartAgency').value = data.agency ?? ''; form.querySelector('#editPartSector').value = data.sector ?? ''; form.querySelector('#editPartProject').value = data.project ?? ''; form.querySelector('#editPartPerson').value = data.person ?? ''; form.querySelector('#editPartRemarks').value = data.remarks ?? ''; modal.querySelector('#editParticipantModalTitle').textContent = 'Edit Participant'; form.style.display = 'grid'; saveButton.disabled = false; } catch (populateError) { console.error("Error populating participant edit form:", populateError); errorIndicator.textContent = `Error displaying data: ${populateError.message}.`; errorIndicator.style.display = 'block'; } } else { errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve participant data.'}`; errorIndicator.style.display = 'block'; } }
         async function fetchTech4edData(id) {
            console.log(`Fetching Tech4ED DTC data for ID: ${id}`);
            const modal = modals.editTech4ed;
            const form = modal.querySelector('#editTech4edForm');
            const loadingIndicator = modal.querySelector('#editTech4edLoadingIndicator');
            const errorIndicator = modal.querySelector('#editTech4edErrorIndicator');
            const saveButton = modal.querySelector('#saveEditTech4edButton');

            loadingIndicator.style.display = 'flex';
            errorIndicator.style.display = 'none';
            form.style.display = 'none';
            form.reset();
            saveButton.disabled = true;

            const result = await performAjax('getTech4ed', { id: id });
            loadingIndicator.style.display = 'none';

            if (result?.success && result.data) {
                const data = result.data;
                try {
                    // Populate form fields with data
                    form.querySelector('#editTech4edFormId').value = data.id || '';
                    form.querySelector('#editTech4edRegion').value = data.region ?? '';
                    form.querySelector('#editTech4edProvince').value = data.province ?? '';
                    form.querySelector('#editTech4edDistrict').value = data.district ?? '';
                    form.querySelector('#editTech4edMunicipality').value = data.municipality ?? '';
                    form.querySelector('#editTech4edBarangay').value = data.barangay ?? '';
                    form.querySelector('#editTech4edStreet').value = data.street ?? '';
                    form.querySelector('#editTech4edLocation').value = data.location ?? '';
                    form.querySelector('#editTech4edCname').value = data.cname ?? '';
                    form.querySelector('#editTech4edHost').value = data.host ?? '';
                    form.querySelector('#editTech4edCategory').value = data.category ?? '';
                    form.querySelector('#editTech4edLongitude').value = data.longitude ?? '';
                    form.querySelector('#editTech4edLatitude').value = data.latitude ?? '';
                    form.querySelector('#editTech4edCmanager').value = data.cmanager ?? '';
                    form.querySelector('#editTech4edCemail').value = data.cemail ?? '';
                    form.querySelector('#editTech4edCmobile').value = data.cmobile ?? '';
                    form.querySelector('#editTech4edClandline').value = data.clandline ?? '';

                    // Handle select elements
                    const cgenderSelect = form.querySelector('#editTech4edCgender');
                    const cgenderValue = data.cgender ?? '';
                    Array.from(cgenderSelect.options).forEach(option => {
                        option.selected = (option.value === cgenderValue);
                    });

                    form.querySelector('#editTech4edAmanager').value = data.amanager ?? '';
                    form.querySelector('#editTech4edAemail').value = data.aemail ?? '';
                    form.querySelector('#editTech4edAmobile').value = data.amobile ?? '';
                    form.querySelector('#editTech4edAlandline').value = data.alandline ?? '';

                    // Handle select elements
                    const agenderSelect = form.querySelector('#editTech4edAgender');
                    const agenderValue = data.agender ?? '';
                    Array.from(agenderSelect.options).forEach(option => {
                        option.selected = (option.value === agenderValue);
                    });

                    // Format dates
                    form.querySelector('#editTech4edLaunch').value = formatDateForInputJS(data.launch);
                    form.querySelector('#editTech4edRegistration').value = formatDateForInputJS(data.registration);
                    form.querySelector('#editTech4edOperation').value = formatDateForInputJS(data.operation);
                    form.querySelector('#editTech4edVisited').value = formatDateForInputJS(data.visited);
                    form.querySelector('#editTech4edExpiration').value = formatDateForInputJS(data.expiration);
                    form.querySelector('#editTech4edDatedonation').value = formatDateForInputJS(data.datedonation);

                    // Equipment
                    form.querySelector('#editTech4edDesktop').value = data.desktop ?? '0';
                    form.querySelector('#editTech4edLaptop').value = data.laptop ?? '0';
                    form.querySelector('#editTech4edPrinter').value = data.printer ?? '0';
                    form.querySelector('#editTech4edScanner').value = data.scanner ?? '0';

                    // Status and connectivity
                    form.querySelector('#editTech4edStatus').value = data.status ?? '';
                    form.querySelector('#editTech4edNetwork').value = data.network ?? '';
                    form.querySelector('#editTech4edConnectivity').value = data.connectivity ?? '';
                    form.querySelector('#editTech4edSpeed').value = data.speed ?? '';

                    // Training
                    form.querySelector('#editTech4edCmtmale').value = data.cmtmale ?? '0';
                    form.querySelector('#editTech4edCmtfemale').value = data.cmtfemale ?? '0';
                    form.querySelector('#editTech4edStraining').value = data.straining ?? '';
                    form.querySelector('#editTech4edEtraining').value = data.etraining ?? '';

                    // Additional info
                    form.querySelector('#editTech4edSigning').value = data.signing ?? '';
                    form.querySelector('#editTech4edPartner').value = data.partner ?? '';
                    form.querySelector('#editTech4edDonation').value = data.donation ?? '';
                    form.querySelector('#editTech4edTcms').value = data.tcms ?? '';
                    form.querySelector('#editTech4edKeyOne').value = data.key_one ?? '';
                    form.querySelector('#editTech4edIdentifier').value = data.identifier ?? '';

                    modal.querySelector('#editTech4edModalTitle').textContent = 'Edit Tech4ED DTC';
                    form.style.display = 'grid';
                    saveButton.disabled = false;
                } catch (populateError) {
                    console.error("Error populating Tech4ED DTC edit form:", populateError);
                    errorIndicator.textContent = `Error displaying data: ${populateError.message}.`;
                    errorIndicator.style.display = 'block';
                }
            } else {
                errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve Tech4ED DTC data.'}`;
                errorIndicator.style.display = 'block';
            }
         }
         async function fetchActivityFiles(activityIds) { console.log("Fetching files for Activity IDs:", activityIds); if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active'); if(fileGroupContainer) fileGroupContainer.innerHTML = ''; if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none'; const result = await performAjax('getFiles', { ids: activityIds }); if(viewLoadingIndicator) viewLoadingIndicator.classList.remove('active'); if (result.success && result.data?.groupedFiles) { populateFileGroups(result.data.groupedFiles, result.data.totalFiles, 'activity'); } else { if(viewFilesErrorMessage) { viewFilesErrorMessage.textContent = `Error loading files: ${result.message || 'Unknown error'}`; viewFilesErrorMessage.style.display = 'block'; } console.error("Error fetching activity files:", result.message); updateViewFilesControls(); } }
         async function fetchParticipantFiles(participantIds) { console.log("Fetching files for Participant IDs:", participantIds); if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active'); if(fileGroupContainer) fileGroupContainer.innerHTML = ''; if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none'; const result = await performAjax('getParticipantFiles', { ids: participantIds }); if(viewLoadingIndicator) viewLoadingIndicator.classList.remove('active'); if (result.success && result.data?.groupedFiles) { populateFileGroups(result.data.groupedFiles, result.data.totalFiles, 'participant'); } else { if(viewFilesErrorMessage) { viewFilesErrorMessage.textContent = `Error loading files: ${result.message || 'Unknown error'}`; viewFilesErrorMessage.style.display = 'block'; } console.error("Error fetching participant files:", result.message); updateViewFilesControls(); } }

         async function fetchTech4edFiles(tech4edIds) {
             console.log("Fetching files for Tech4ED DTC IDs:", tech4edIds);
             if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active');
             if(fileGroupContainer) fileGroupContainer.innerHTML = '';
             if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none';
             if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none';

             const result = await performAjax('getTech4edFiles', { ids: tech4edIds });

             if(viewLoadingIndicator) viewLoadingIndicator.classList.remove('active');

             if (result.success && result.data?.groupedFiles) {
                 populateFileGroups(result.data.groupedFiles, result.data.totalFiles, 'tech4ed');
             } else {
                 if(viewFilesErrorMessage) {
                     viewFilesErrorMessage.textContent = `Error loading files: ${result.message || 'Unknown error'}`;
                     viewFilesErrorMessage.style.display = 'block';
                 }
                 console.error("Error fetching Tech4ED DTC files:", result.message);
                 updateViewFilesControls();
             }
         }
         function populateFileGroups(groupedFiles, totalFiles, itemType = 'activity') { if(fileGroupContainer) fileGroupContainer.innerHTML = ''; if (totalFiles === 0) { if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'block'; if(viewFilesSelectAll) viewFilesSelectAll.disabled = true; if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; return; } else { if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if(viewFilesSelectAll) viewFilesSelectAll.disabled = false; if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; } let groupIndex = 0; for (const groupName in groupedFiles) { const filesInGroup = groupedFiles[groupName]; const groupDiv = document.createElement('div'); groupDiv.className = 'file-group collapsed'; const headerDiv = document.createElement('div'); headerDiv.className = 'file-group-header'; headerDiv.innerHTML = `<button class="toggle-group"><i class="fas fa-chevron-right"></i></button><span class="group-title" title="${escapeHtml(groupName)}">${escapeHtml(groupName)}</span><span class="badge">${filesInGroup.length} file(s)</span>`; const contentDiv = document.createElement('div'); contentDiv.className = 'file-group-content'; filesInGroup.forEach(file => { const safeFilename = escapeHtml(file.original_filename || 'Unnamed File'); const fileIconClass = getFileIconClass(safeFilename); const downloadAction = itemType === 'participant' ? 'downloadParticipantFile' :
                                 itemType === 'tech4ed' ? 'downloadTech4edFile' : 'downloadFile'; const downloadUrl = `${ajaxHandlerUrl}?action=${downloadAction}&file_id=${file.file_id}`; const fileItemDiv = document.createElement('div'); fileItemDiv.className = 'file-item'; fileItemDiv.dataset.fileId = file.file_id; const previewUrl = `${ajaxHandlerUrl}?action=previewFile&file_id=${file.file_id}&type=${itemType}`;
fileItemDiv.innerHTML = `<input type="checkbox" class="file-select-checkbox" value="${file.file_id}"><i class="fas ${fileIconClass} file-icon"></i><div class="file-details"><span class="file-name" title="${safeFilename}">${safeFilename}</span><span class="file-meta">Uploaded: ${escapeHtml(file.formatted_uploaded_at || 'N/A')}</span></div><span class="file-size">${escapeHtml(file.formatted_filesize || 'N/A')}</span><a href="${previewUrl}" class="btn-icon btn-preview-file" title="Preview ${safeFilename}" target="_blank"><i class="fas fa-eye"></i></a><a href="${downloadUrl}" class="btn-icon btn-download-file" title="Download ${safeFilename}"><i class="fas fa-download"></i></a>`;
contentDiv.appendChild(fileItemDiv);
fileItemDiv.querySelector('.file-select-checkbox').addEventListener('change', updateViewFilesControls);
fileItemDiv.querySelector('.btn-download-file').addEventListener('click', function(e) {
    e.preventDefault();
    console.log("Download URL:", this.getAttribute('href'));
    window.location.href = this.getAttribute('href');
}); }); groupDiv.appendChild(headerDiv); groupDiv.appendChild(contentDiv); fileGroupContainer.appendChild(groupDiv); headerDiv.addEventListener('click', (e) => { if(e.target.tagName === 'INPUT') return; const group = headerDiv.closest('.file-group'); group.classList.toggle('collapsed'); const icon = headerDiv.querySelector('.toggle-group i'); icon.className = group.classList.contains('collapsed') ? 'fas fa-chevron-right' : 'fas fa-chevron-down'; }); if (groupIndex === 0) { groupDiv.classList.remove('collapsed'); headerDiv.querySelector('.toggle-group i').className = 'fas fa-chevron-down'; } groupIndex++; } updateViewFilesControls(); }
         function updateViewFilesControls() { if (!viewFilesModal) return; const allCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox'); const checkedCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox:checked'); const totalCount = allCheckboxes.length; const checkedCount = checkedCheckboxes.length; if (totalCount === 0) { if(viewFilesSelectAll){ viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true;} if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; } else { if(viewFilesSelectAll){ viewFilesSelectAll.disabled = false; if (checkedCount === 0) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; } else if (checkedCount === totalCount) { viewFilesSelectAll.checked = true; viewFilesSelectAll.indeterminate = false; } else { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = true; }} if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = (checkedCount === 0); } }

         // --- Target Management Functions ---
         async function openManageTargetsModal() {
             if (!modals.manageTargets) return;
             console.log('Opening manage targets modal for ILCDB...');
             resetTargetForm(); // Reset form first
             if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'flex';
             if(targetsErrorIndicator) targetsErrorIndicator.style.display = 'none';
             if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="7" class="table-message">Loading...</td></tr>';

             try {
                 // Fetch targets for the specific category
                 console.log('Fetching targets for category: <?php echo $target_category_filter; ?>');
                 const result = await performAjax('getTargets', {
                     category: '<?php echo $target_category_filter; ?>' // <-- Use Correct PHP Variable
                     // Year filter is removed here, modal shows all years for the category
                 });

                 console.log('Targets fetch result:', result);

                 if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'none';
                 if (result.success && result.data) {
                     console.log('Populating targets table with', result.data.length, 'targets');
                     populateTargetsTable(result.data);
                 } else {
                     console.error('Failed to load targets:', result.message);
                     if(targetsErrorIndicator) {
                         targetsErrorIndicator.textContent = `Error loading targets: ${result.message || 'Unknown error'}`;
                         targetsErrorIndicator.style.display = 'block';
                     }
                     if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="7" class="table-message error">Could not load targets.</td></tr>';
                 }
             } catch (error) {
                 console.error('Exception in openManageTargetsModal:', error);
                 if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'none';
                 if(targetsErrorIndicator) {
                     targetsErrorIndicator.textContent = `Error loading targets: ${error.message}`;
                     targetsErrorIndicator.style.display = 'block';
                 }
                 if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="7" class="table-message error">Could not load targets.</td></tr>';
             }
             // Modal visibility is handled by the calling openModal function
         }

         function populateTargetsTable(targets) {
             console.log('populateTargetsTable called with:', targets);
             if (!targetsTableBody) {
                 console.error('targetsTableBody element not found!');
                 return;
             }
             targetsTableBody.innerHTML = ''; // Clear existing

             if (!targets || targets.length === 0) {
                 console.log('No targets to display');
                 targetsTableBody.innerHTML = '<tr><td colspan="7" class="table-message">No targets defined for this category yet.</td></tr>';
                 return;
             }

             console.log('Displaying', targets.length, 'targets');

             // Sort targets for display (optional, can be done in SQL too)
             targets.sort((a, b) => {
                if (a.year !== b.year) return b.year - a.year; // Descending year
                const subcatCompare = (a.subcategory || '').localeCompare(b.subcategory || '');
                if (subcatCompare !== 0) return subcatCompare; // Ascending subcategory
                return (a.indicator || '').localeCompare(b.indicator || ''); // Ascending indicator
             });

             targets.forEach((target, index) => {
                 console.log(`Adding target ${index + 1}:`, target);
                 const row = targetsTableBody.insertRow();
                 row.dataset.targetId = target.id;
                 row.innerHTML = `
                     <td>${escapeHtml(target.category)}</td>
                     <td>${escapeHtml(target.subcategory)}</td>
                     <td>${escapeHtml(target.indicator)}</td>
                     <td>${escapeHtml(target.year)}</td>
                     <td>${escapeHtml(target.target)}</td>
                     <td>${target.target_participants !== null ? escapeHtml(target.target_participants) : '<span class="text-muted">-</span>'}</td>
                     <td class="actions-col">
                         <button class="btn-icon edit-target" title="Edit Target" data-id="${target.id}"><i class="fas fa-edit"></i></button>
                         <button class="btn-icon delete-target" title="Delete Target" data-id="${target.id}"><i class="fas fa-trash"></i></button>
                     </td>
                 `;
                 // Add event listeners for edit/delete buttons
                 row.querySelector('.edit-target').addEventListener('click', () => editTarget(target));
                 row.querySelector('.delete-target').addEventListener('click', () => deleteTarget(target.id));
             });
             console.log('Targets table populated successfully');
         }

         function editTarget(targetData) {
            if (!targetForm || !targetData) return;
            targetEditIdInput.value = targetData.id;
            targetFormTitle.textContent = 'Edit Target';
            document.getElementById('targetCategory').value = targetData.category || '<?php echo htmlspecialchars($target_category_filter, ENT_QUOTES); ?>'; // <-- Use Correct PHP Variable
            document.getElementById('targetSubcategory').value = targetData.subcategory || '';
            document.getElementById('targetIndicator').value = targetData.indicator || '';
            document.getElementById('targetYear').value = targetData.year || currentReportYear; // Use current report year as fallback
            document.getElementById('targetValue').value = targetData.target || '';
            document.getElementById('targetParticipantsValue').value = targetData.target_participants || '';
            saveTargetButton.innerHTML = '<i class="fas fa-save"></i> Update Target';
            document.getElementById('targetSubcategory').focus(); // Focus on first editable field
         }

         async function deleteTarget(targetId) {
             const result = await performAjax('deleteTarget', { id: targetId });
             if (result.success) {
                 showNotification(result.message || 'Target deleted successfully!', 'success');
                 // Refresh the table IF the modal is currently visible
                 if (modals.manageTargets && modals.manageTargets.classList.contains('visible')) {
                     openManageTargetsModal();
                 }
                 fetchAndRenderGraphs(); // Refresh graphs outside the modal
             } else {
                 showNotification(`Error deleting target: ${result.message || 'Unknown error'}`, 'error', 5000);
             }
         }

         // --- Report/Graph Functions ---
         async function fetchAndRenderGraphs() {
             console.log(`Fetching report data for year: ${currentReportYear}`);
             console.log(`Category: <?php echo $target_category_filter; ?>`);

             if(reportLoadingIndicator) reportLoadingIndicator.classList.add('active');
             if(reportGraphContainer) reportGraphContainer.innerHTML = `<div class="loading-indicator active" style="width: 100%; padding: 40px; background-color: var(--bg-light); border-radius: var(--border-radius); border: 1px solid var(--border-color);"><i class="fas fa-spinner fa-spin"></i> Loading report data for ${currentReportYear}...</div>`; // Clear previous graphs and show loading

             // Destroy existing charts
             Object.values(reportCharts).forEach(chart => chart.destroy());
             reportCharts = {};

             try {
                 const result = await performAjax('getReportData', {
                     category: '<?php echo $target_category_filter; ?>', // <-- Use Correct PHP Variable
                     year: currentReportYear // Use the JS variable
                 });

                 console.log('Report data result:', result);

                 // Ensure loading indicator is removed regardless of success/failure
                 if(reportLoadingIndicator) reportLoadingIndicator.classList.remove('active'); // Hide outer loading indicator if it exists
                 if(reportGraphContainer) reportGraphContainer.innerHTML = ''; // Clear container before rendering

             if (result.success && result.data && result.data.length > 0) {
                 result.data.forEach((item, index) => {
                     const wrapper = document.createElement('div');
                     wrapper.className = 'graph-wrapper';

                     const title = document.createElement('h4');
                     // Use subcategory, fallback to indicator if subcategory is missing/null
                     const graphTitleText = item.subcategory ? escapeHtml(item.subcategory) : escapeHtml(item.indicator);
                     // Add indicator to title if different from subcategory for clarity
                     let titleText = '';
                     if (item.indicator && item.indicator !== item.subcategory) {
                         titleText = `${graphTitleText} - ${escapeHtml(item.indicator)} (${escapeHtml(item.year)})`;
                     } else {
                         titleText = `${graphTitleText} (${escapeHtml(item.year)})`;
                     }

                     // Add type indicator if it's a participant target
                     if (item.type === 'participant') {
                         titleText += ' - Participants';
                     } else if (item.type === 'activity') {
                         titleText += ' - Activities';
                     }

                     title.textContent = titleText;
                     wrapper.appendChild(title);

                     const canvas = document.createElement('canvas');
                     canvas.id = `reportChart_${index}`;
                     wrapper.appendChild(canvas);

                     if(reportGraphContainer) reportGraphContainer.appendChild(wrapper);

                     const ctx = canvas.getContext('2d');
                     if (typeof Chart === 'undefined') {
                         console.error('Chart.js is not loaded!');
                         wrapper.innerHTML = '<div style="padding: 20px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">Error: Chart.js library not loaded</div>';
                         return;
                     }
                     reportCharts[canvas.id] = new Chart(ctx, {
                         type: 'bar', // Changed to bar for Target vs Accomplishment
                         data: {
                             labels: ['Target', 'Accomplishment'],
                             datasets: [{
                                 label: 'Count', // Keep simple label
                                 data: [item.target, item.accomplishment],
                                 backgroundColor: [
                                     'rgba(255, 159, 64, 0.6)', // Orange for Target
                                     'rgba(75, 192, 192, 0.6)'  // Teal for Accomplishment
                                 ],
                                 borderColor: [
                                     'rgba(255, 159, 64, 1)',
                                     'rgba(75, 192, 192, 1)'
                                 ],
                                 borderWidth: 1,
                                 barPercentage: 0.6, // Adjust bar thickness
                                 categoryPercentage: 0.7 // Adjust space between bars if needed
                             }]
                         },
                         options: {
                             responsive: true,
                             maintainAspectRatio: false,
                             indexAxis: 'y', // Horizontal bars often look better for few categories
                             plugins: {
                                 legend: { display: false },
                                 title: { display: false }, // Use the h4 tag above canvas instead
                                 tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) { label += ': '; }
                                            if (context.parsed.x !== null) { label += context.parsed.x; }
                                            return label;
                                        }
                                    }
                                 }
                             },
                             scales: {
                                 x: { // X axis for horizontal bars
                                     beginAtZero: true,
                                     ticks: {
                                         precision: 0,
                                         stepSize: Math.max(1, Math.ceil(Math.max(item.target, item.accomplishment) / 5)) // Dynamic step size
                                     }
                                 },
                                 y: { // Y axis (categories)
                                    ticks: {
                                        font: { size: 11 } // Adjust font size if needed
                                    }
                                 }
                             }
                         }
                     });
                 });
                 } else if (result.success && (!result.data || result.data.length === 0)) {
                     console.log('No targets found for category and year');
                     if(reportGraphContainer) reportGraphContainer.innerHTML = '<div class="table-message" style="width: 100%; padding: 30px; background-color: var(--bg-light); border-radius: var(--border-radius); border: 1px solid var(--border-color);">No targets found for <?php echo htmlspecialchars($target_category_filter); ?> in year ' + currentReportYear + '. Please add targets using the "Manage Targets" button.</div>'; // <-- Changed Message
                 }
                 else {
                     console.error("Error fetching report data:", result.message);
                     if(reportGraphContainer) reportGraphContainer.innerHTML = `<div class="table-message error" style="width: 100%; padding: 30px; background-color: var(--bg-light); border-radius: var(--border-radius); border: 1px solid var(--border-color);"><i class="fas fa-exclamation-triangle"></i> Error loading report data for ${currentReportYear}: ${result.message || 'Unknown error'}</div>`;
                 }
             } catch (error) {
                 console.error("Exception in fetchAndRenderGraphs:", error);
                 if(reportLoadingIndicator) reportLoadingIndicator.classList.remove('active');
                 if(reportGraphContainer) reportGraphContainer.innerHTML = `<div class="table-message error" style="width: 100%; padding: 30px; background-color: var(--bg-light); border-radius: var(--border-radius); border: 1px solid var(--border-color);"><i class="fas fa-exclamation-triangle"></i> Error loading report data: ${error.message || 'Unknown error'}</div>`;
             }
         }


         // --- Event Listeners ---
         if (importButton) {
             importButton.addEventListener('click', () => {
                 currentImportType = currentActiveTab === 'participants' ? 'participant' :
                                    currentActiveTab === 'tech4ed' ? 'tech4ed' :
                                    'activity';
                 updateImportModalContent(currentImportType);
                 openModal('import');
             });
         }
         if (exportButton) {
             exportButton.addEventListener('click', () => {
                 currentExportType = currentActiveTab === 'participants' ? 'participant' :
                                    currentActiveTab === 'tech4ed' ? 'tech4ed' :
                                    'activity';
                 updateExportModalContent(currentExportType);
                 openModal('export');
             });
         }
         if (exportDataButton) { exportDataButton.addEventListener('click', async () => {
             const exportType = document.querySelector('input[name="exportType"]:checked')?.value || 'all';
             const exportFormat = document.querySelector('input[name="exportFormat"]:checked')?.value || 'csv';
             const activeType = currentActiveTab === 'participants' ? 'participant' :
                               currentActiveTab === 'tech4ed' ? 'tech4ed' :
                               'activity';

             let action;
             if (activeType === 'activity') {
                 action = 'exportActivities';
             } else if (activeType === 'tech4ed') {
                 action = 'exportTech4ed';
             } else {
                 action = 'exportParticipants';
             }

             const typeName = activeType.charAt(0).toUpperCase() + activeType.slice(1);
             const typeNamePlural = typeName + 's';

             exportProcessingIndicator.style.display = 'flex';
             exportErrorIndicator.style.display = 'none';
             exportDataButton.disabled = true;

             const exportFormData = new FormData();
             exportFormData.append('action', action);
             exportFormData.append('format', exportFormat);
             exportFormData.append('type', exportType);
             exportFormData.append('project', '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'); // <-- Pass correct project filter
             const currentParams = new URLSearchParams(window.location.search); if (exportType === 'filtered') { if (activeType === 'activity') { exportFormData.append('search', currentParams.get('search') || ''); <?php foreach ($filter_columns_activity as $column): ?> exportFormData.append('<?php echo $column; ?>', currentParams.get('<?php echo $column; ?>') || ''); <?php endforeach; ?> } else { exportFormData.append('p_search', currentParams.get('p_search') || ''); <?php foreach ($filter_columns_participant as $column): ?> exportFormData.append('p_<?php echo $column; ?>', currentParams.get('p_<?php echo $column; ?>') || ''); <?php endforeach; ?> } } else if (exportType === 'selected') { const selectedIds = getSelectedIds(activeType); if (selectedIds.length === 0) { showNotification(`No ${typeNamePlural.toLowerCase()} selected for export.`, "warning", 3000); exportProcessingIndicator.style.display = 'none'; exportDataButton.disabled = false; return; } selectedIds.forEach(id => exportFormData.append('ids[]', id)); } try { const response = await fetch(ajaxHandlerUrl, { method: 'POST', body: exportFormData }); if (!response.ok) { let errorMsg = `Export failed (Status: ${response.status}).`; try { const errData = await response.json(); errorMsg = errData.message || errorMsg; } catch (e) { errorMsg = await response.text(); } throw new Error(errorMsg); } const disposition = response.headers.get('Content-Disposition'); let filename = `${activeType}s_export_${new Date().toISOString().slice(0,10)}.csv`; if (disposition && disposition.indexOf('attachment') !== -1) { const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/; const matches = filenameRegex.exec(disposition); if (matches != null && matches[1]) { filename = matches[1].replace(/['"]/g, ''); } } const blob = await response.blob(); const url = window.URL.createObjectURL(blob); const a = document.createElement('a'); a.style.display = 'none'; a.href = url; a.download = filename; document.body.appendChild(a); a.click(); window.URL.revokeObjectURL(url); a.remove(); showNotification(`${typeName} export successful. Download started.`, 'success'); closeModal('export'); } catch (error) { console.error('Export error:', error); exportErrorIndicator.textContent = `Error during export: ${error.message}`; exportErrorIndicator.style.display = 'block'; showNotification(`Export failed: ${error.message}`, 'error', 5000); } finally { exportProcessingIndicator.style.display = 'none'; exportDataButton.disabled = false; } }); }

         tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = tab.getAttribute('data-tab');
                if (tabId === currentActiveTab) return;

                const previousActiveTab = currentActiveTab;
                currentActiveTab = tabId;

                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                tab.classList.add('active');
                const activeTabContent = document.getElementById(tabId + '-tab');
                if (activeTabContent) activeTabContent.classList.add('active');

                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('tab', tabId);
                // Also update the year param in URL when switching tabs if it came from the filter
                 currentUrl.searchParams.set('year', currentReportYear);
                window.history.pushState({ path: currentUrl.href }, '', currentUrl.href);

                // --- Manage button/filter visibility using JS ---
                const dynamicImportButton = document.getElementById('importButton');
                const dynamicExportButton = document.getElementById('exportButton');
                const dynamicManageTargetsButton = document.getElementById('manageTargetsButton');
                const dynamicReportYearFilter = document.getElementById('reportYearFilter'); // Get year filter


                if (currentActiveTab === 'reports') {
                    if (dynamicImportButton) dynamicImportButton.style.display = 'none';
                    if (dynamicExportButton) dynamicExportButton.style.display = 'none';
                    if (dynamicManageTargetsButton) dynamicManageTargetsButton.style.display = 'inline-flex'; // Show
                    if (dynamicReportYearFilter) dynamicReportYearFilter.style.display = 'inline-block'; // Show
                    if (previousActiveTab !== 'reports') {
                        fetchAndRenderGraphs(); // Fetch graphs only when switching *to* reports
                    }
                } else {
                     if (dynamicManageTargetsButton) dynamicManageTargetsButton.style.display = 'none'; // Hide
                     if (dynamicReportYearFilter) dynamicReportYearFilter.style.display = 'none'; // Hide
                     // Ensure Import/Export are visible (re-add if needed, simpler to just show/hide)
                     if (dynamicImportButton) dynamicImportButton.style.display = 'inline-flex';
                     if (dynamicExportButton) dynamicExportButton.style.display = 'inline-flex';
                }
                // --- End button visibility ---

                updateActionBar(); // Update for potential selection changes
                // Clear selections when switching tabs
                if (selectAllCheckboxActivity) selectAllCheckboxActivity.checked = false;
                if (selectAllCheckboxParticipant) selectAllCheckboxParticipant.checked = false;
                // Add for Tech4ED if/when a selectAllCheckboxTech4ed is created
                activityRowCheckboxes.forEach(cb => cb.checked = false);
                participantRowCheckboxes.forEach(cb => cb.checked = false);
                // Add for Tech4ED if/when tech4edRowCheckboxes are created
                updateActionBar(); // Update again after clearing
            });
         });


         if (selectAllCheckboxActivity) { selectAllCheckboxActivity.addEventListener('change', () => { const isChecked = selectAllCheckboxActivity.checked; activityRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         if (selectAllCheckboxParticipant) { selectAllCheckboxParticipant.addEventListener('change', () => { const isChecked = selectAllCheckboxParticipant.checked; participantRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         if (selectAllCheckboxTech4ed) { selectAllCheckboxTech4ed.addEventListener('change', () => { const isChecked = selectAllCheckboxTech4ed.checked; tech4edRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         activityRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         participantRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         tech4edRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         if(closeActionBarButton) {
             closeActionBarButton.addEventListener('click', () => {
                 const activeType = currentActiveTab === 'participants' ? 'participant'
                                  : currentActiveTab === 'tech4ed' ? 'tech4ed'
                                  : 'activity';

                 let selectAllCheckbox, rowCheckboxes;
                 if (activeType === 'activity') {
                     selectAllCheckbox = selectAllCheckboxActivity;
                     rowCheckboxes = activityRowCheckboxes;
                 } else if (activeType === 'participant') {
                     selectAllCheckbox = selectAllCheckboxParticipant;
                     rowCheckboxes = participantRowCheckboxes;
                 } else if (activeType === 'tech4ed') {
                     selectAllCheckbox = selectAllCheckboxTech4ed;
                     rowCheckboxes = tech4edRowCheckboxes;
                 }

                 if (selectAllCheckbox) selectAllCheckbox.checked = false;
                 rowCheckboxes.forEach(cb => { cb.checked = false; });
                 updateActionBar();
             });
         }
         if (addActivityButton) { addActivityButton.addEventListener('click', () => openModal('addActivity')); }
         if (addParticipantButton) { addParticipantButton.addEventListener('click', () => openModal('addParticipant')); }

         // Add Tech4ED DTC button
         const addTech4edButton = document.getElementById('addTech4edButton');
         if (addTech4edButton) {
             addTech4edButton.addEventListener('click', () => {
                 openModal('addTech4ed');
             });
         }
         if (editButton) {
             editButton.addEventListener('click', () => {
                 if (!editButton.disabled) {
                     const modalKey = currentActiveTab === 'participants' ? 'editParticipant'
                                    : currentActiveTab === 'tech4ed' ? 'editTech4ed'
                                    : 'editActivity';

                     openModal(modalKey);
                 }
             });
         }
         if (deleteButton) { deleteButton.addEventListener('click', () => { if (!deleteButton.disabled) { const activeType = currentActiveTab === 'participants' ? 'participant'
                                      : currentActiveTab === 'tech4ed' ? 'tech4ed' // Add tech4ed type
                                      : 'activity';
                     const selectedIds = getSelectedIds(activeType);
                     deleteContext = { type: activeType, ids: selectedIds };
                     openModal('delete'); } }); }
         if (uploadButton) { uploadButton.addEventListener('click', () => { if (!uploadButton.disabled) openModal('upload'); }); }
         if (viewFilesButton) {
             viewFilesButton.addEventListener('click', () => {
                 if (!viewFilesButton.disabled) {
                     const activeType = currentActiveTab === 'participants' ? 'participant'
                                      : currentActiveTab === 'tech4ed' ? 'tech4ed'
                                      : 'activity';

                     // Tech4ED view files functionality is now implemented

                     openModal('view');
                 }
             });
         }
         document.querySelectorAll('.close-modal').forEach(button => { button.addEventListener('click', () => { const modalId = button.closest('.modal')?.id; const modalKey = Object.keys(modals).find(key => modals[key]?.id === modalId); if(modalKey) { closeModal(modalKey); } else { console.warn("Could not find modal key for ID:", modalId); button.closest('.modal')?.classList.remove('visible'); if (!Object.values(modals).some(m => m?.classList.contains('visible'))) { document.body.style.overflow = ''; } } }); });
         Object.values(modals).forEach(modal => { if(modal) { modal.addEventListener('click', (event) => { if (event.target === modal) { const modalKey = Object.keys(modals).find(key => modals[key] === modal); if (modalKey) { closeModal(modalKey); } else modal.classList.remove('visible'); } }); } });
         if (viewFilesSelectAll) { viewFilesSelectAll.addEventListener('change', () => { const isChecked = viewFilesSelectAll.checked; viewFilesModal.querySelectorAll('.file-select-checkbox').forEach(cb => { cb.checked = isChecked; }); updateViewFilesControls(); }); }
         if (viewFilesDeleteSelected) { viewFilesDeleteSelected.addEventListener('click', async () => { const checkedCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox:checked'); const fileIdsToDelete = Array.from(checkedCheckboxes).map(cb => cb.value); if (fileIdsToDelete.length === 0) { showNotification("No files selected for deletion.", "info"); return; } viewFilesDeleteSelected.disabled = true; viewFilesDeleteSelected.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...'; const fileTypeParam = currentViewFilesType; const result = await performAjax('deleteMultipleFiles', { file_ids: fileIdsToDelete, type: fileTypeParam }); viewFilesDeleteSelected.innerHTML = '<i class="fas fa-trash"></i> Delete Selected'; if (result.success) { showNotification(result.message || `${result.data?.deleted_count || 0} file(s) deleted successfully.`, 'success'); const parentIds = getSelectedIds(fileTypeParam); if (parentIds.length > 0) {
                if (fileTypeParam === 'activity') fetchActivityFiles(parentIds);
                else if (fileTypeParam === 'tech4ed') fetchTech4edFiles(parentIds);
                else fetchParticipantFiles(parentIds);
            } else { if(fileGroupContainer) fileGroupContainer.innerHTML = ''; if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'block'; updateViewFilesControls(); } } else { showNotification(`Error deleting files: ${result.message || 'Unknown error'}`, 'error', 5000); updateViewFilesControls(); } }); }
         if (fileGroupContainer) { fileGroupContainer.addEventListener('change', (event) => { if (event.target.classList.contains('file-select-checkbox')) { updateViewFilesControls(); } }); }
         if (confirmDeleteButton) {
             confirmDeleteButton.addEventListener('click', async () => {
                 if (!deleteContext || (deleteContext.type !== 'activity' && deleteContext.type !== 'participant' && deleteContext.type !== 'tech4ed')) {
                     console.error("Confirm delete clicked with invalid or missing context:", deleteContext);
                     showNotification("Error: Cannot determine items to delete. Please try again.", "error");
                     closeModal('delete');
                     return;
                 }

                 if (!deleteContext.ids || deleteContext.ids.length === 0) {
                     console.warn("Confirm delete clicked but no IDs found in context:", deleteContext);
                     showNotification("No items were selected for deletion.", "warning");
                     closeModal('delete');
                     return;
                 }

                 // Tech4ED delete functionality is now implemented

                 confirmDeleteButton.disabled = true;
                 confirmDeleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';

                 const deleteAction = deleteContext.type === 'activity' ? 'deleteActivities'
                                    : deleteContext.type === 'participant' ? 'deleteParticipants'
                                    : 'deleteTech4edItems';

                 const result = await performAjax(deleteAction, { ids: deleteContext.ids });

                 confirmDeleteButton.disabled = false;
                 confirmDeleteButton.innerHTML = 'Delete';
                 closeModal('delete');
                 if (result.success) { showNotification(result.message || `Selected ${deleteContext.type}(s) deleted successfully.`, 'success'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', currentActiveTab); if (!currentSearch.get('search')) currentSearch.delete('search'); if (!currentSearch.get('limit')) currentSearch.delete('limit'); window.location.search = currentSearch.toString(); } else { showNotification(`Error deleting ${deleteContext.type}(s): ${result.message || 'Unknown error'}`, 'error', 5000); } }); }

         // Listener for Manage Targets button
         if (manageTargetsButton) {
             manageTargetsButton.addEventListener('click', () => openModal('manageTargets'));
         }

         // Listener for Target Form Reset Button
         if (resetTargetFormButton) {
            resetTargetFormButton.addEventListener('click', resetTargetForm);
         }

         // Listener for Target Form Submission
         if (targetForm && saveTargetButton) {
            targetForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                saveTargetButton.disabled = true;
                saveTargetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const formData = new FormData(targetForm);
                const data = Object.fromEntries(formData.entries());
                const action = data.id ? 'updateTarget' : 'addTarget'; // Determine action based on hidden ID field

                const result = await performAjax(action, data);

                if (result.success) {
                    console.log('Target saved successfully:', result);
                    showNotification(result.message || `Target ${action === 'updateTarget' ? 'updated' : 'added'} successfully!`, 'success');
                    resetTargetForm();
                     // Refresh the table IF the modal is currently visible (might be closed by now)
                    if (modals.manageTargets && modals.manageTargets.classList.contains('visible')) {
                        console.log('Modal is visible, refreshing targets table...');
                        openManageTargetsModal();
                    } else {
                        console.log('Modal is not visible, skipping table refresh');
                    }
                    console.log('Target saved successfully, refreshing graphs...');
                    fetchAndRenderGraphs(); // Refresh graphs outside the modal
                } else {
                    console.error('Failed to save target:', result);
                    showNotification(`Error ${action === 'updateTarget' ? 'updating' : 'adding'} target: ${result.message || 'Unknown error'}`, 'error', 5000);
                    saveTargetButton.disabled = false; // Re-enable button on error
                    saveTargetButton.innerHTML = data.id ? '<i class="fas fa-save"></i> Update Target' : '<i class="fas fa-save"></i> Save Target';
                }
            });
         }

         // Listener for Report Year Filter change
          if (reportYearFilter) {
             reportYearFilter.addEventListener('change', () => {
                 currentReportYear = reportYearFilter.value;
                 console.log("Report year changed to:", currentReportYear);
                 // Update URL without reloading page (optional, but good UX)
                 const currentUrl = new URL(window.location);
                 currentUrl.searchParams.set('year', currentReportYear);
                 window.history.pushState({ path: currentUrl.href }, '', currentUrl.href);
                 // Fetch and render graphs for the new year
                 fetchAndRenderGraphs();
             });
         }


         // --- Form Submissions ---
         // (Existing form submissions for add/edit activity/participant, upload)
         const addActivityForm = document.getElementById('addActivityForm');
         const saveAddActivityButton = document.getElementById('saveAddActivityButton');
         if (addActivityForm && saveAddActivityButton) { addActivityForm.addEventListener('submit', async (event) => { event.preventDefault(); saveAddActivityButton.disabled = true; saveAddActivityButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(addActivityForm); const data = Object.fromEntries(formData.entries()); const result = await performAjax('addActivity', data); if (result.success) { showNotification(result.message || 'Activity added successfully!', 'success'); closeModal('addActivity'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'activities'); window.location.search = currentSearch.toString(); } else { showNotification(`Error adding activity: ${result.message || 'Unknown error'}`, 'error', 5000); saveAddActivityButton.disabled = false; saveAddActivityButton.innerHTML = 'Save Activity'; } }); }
         const addParticipantForm = document.getElementById('addParticipantForm');
         const saveAddParticipantButton = document.getElementById('saveAddParticipantButton');
         if (addParticipantForm && saveAddParticipantButton) { addParticipantForm.addEventListener('submit', async (event) => { event.preventDefault(); saveAddParticipantButton.disabled = true; saveAddParticipantButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(addParticipantForm); const data = Object.fromEntries(formData.entries()); const result = await performAjax('addParticipant', data); if (result.success) { showNotification(result.message || 'Participant added successfully!', 'success'); closeModal('addParticipant'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'participants'); window.location.search = currentSearch.toString(); } else { showNotification(`Error adding participant: ${result.message || 'Unknown error'}`, 'error', 5000); saveAddParticipantButton.disabled = false; saveAddParticipantButton.innerHTML = 'Save Participant'; } }); }
         const editActivityForm = document.getElementById('editActivityForm');
         const saveEditActivityButton = document.getElementById('saveEditActivityButton');
         if (editActivityForm && saveEditActivityButton) { editActivityForm.addEventListener('submit', async (event) => { event.preventDefault(); saveEditActivityButton.disabled = true; saveEditActivityButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(editActivityForm); const data = Object.fromEntries(formData.entries()); data.id = document.getElementById('editActivityFormId').value; const result = await performAjax('updateActivity', data); if (result.success) { showNotification(result.message || 'Activity updated successfully!', 'success'); closeModal('editActivity'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'activities'); window.location.search = currentSearch.toString(); } else { showNotification(`Error updating activity: ${result.message || 'Unknown error'}`, 'error', 5000); saveEditActivityButton.disabled = false; saveEditActivityButton.innerHTML = 'Save Changes'; } }); }
         const editParticipantForm = document.getElementById('editParticipantForm');
         const saveEditParticipantButton = document.getElementById('saveEditParticipantButton');
         if (editParticipantForm && saveEditParticipantButton) { editParticipantForm.addEventListener('submit', async (event) => { event.preventDefault(); saveEditParticipantButton.disabled = true; saveEditParticipantButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(editParticipantForm); const data = Object.fromEntries(formData.entries()); data.id = document.getElementById('editParticipantFormId').value; const result = await performAjax('updateParticipant', data); if (result.success) { showNotification(result.message || 'Participant updated successfully!', 'success'); closeModal('editParticipant'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'participants'); window.location.search = currentSearch.toString(); } else { showNotification(`Error updating participant: ${result.message || 'Unknown error'}`, 'error', 5000); saveEditParticipantButton.disabled = false; saveEditParticipantButton.innerHTML = 'Save Changes'; } }); }
         if (uploadForm && submitUploadButton) {
             uploadForm.addEventListener('submit', async (event) => {
                 event.preventDefault();
                 const files = fileInput?.files;
                 const activeType = currentActiveTab === 'participants' ? 'participant'
                                  : currentActiveTab === 'tech4ed' ? 'tech4ed'
                                  : 'activity';
                 const parentIdsToUpload = getSelectedIds(activeType);

                 if (!files || files.length === 0) {
                     showNotification('Please select at least one file.', 'error');
                     return;
                 }
                 if (parentIdsToUpload.length === 0) {
                     showNotification(`No ${activeType}s selected for file upload.`, 'error');
                     return;
                 }

                 // Tech4ED upload functionality is now implemented

                 submitUploadButton.disabled = true;
                 submitUploadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

                 const uploadAction = activeType === 'activity' ? 'uploadFiles'
                                    : activeType === 'participant' ? 'uploadParticipantFiles'
                                    : 'uploadTech4edFiles';

                 const result = await performAjax(uploadAction, { ids: parentIdsToUpload, files: files });

                 submitUploadButton.disabled = false;
                 submitUploadButton.innerHTML = '<i class="fas fa-upload"></i> Upload';
                 if (result.success) {
                     showNotification(result.message || `${result.data?.uploaded_count || '?'} file(s) uploaded successfully!`, 'success');
                     closeModal('upload');
                 } else {
                     showNotification(`Error uploading files: ${result.message || 'Unknown error'}`, 'error', 5000);
                 }
             }); }

         // --- Import Listeners ---
         if (csvFileInput) { csvFileInput.addEventListener('change', (event) => { selectedFile = event.target.files[0]; if (selectedFile) { if (!selectedFile.name.toLowerCase().endsWith('.csv')) { showImportError('Please select a CSV file.', 1); fileNameDisplay.textContent = 'Invalid file type'; selectedFile = null; previewDataButton.disabled = true; return; } fileNameDisplay.textContent = selectedFile.name; previewDataButton.disabled = false; importDataButton.disabled = true; importErrorStep1.style.display = 'none'; importStep2.style.display = 'none'; previewTableContainer.style.display = 'none'; } else { fileNameDisplay.textContent = 'No file chosen'; previewDataButton.disabled = true; selectedFile = null; } }); }
         if (previewDataButton) { previewDataButton.addEventListener('click', () => { if (!selectedFile) { showImportError('No file selected.', 1); return; } previewDataButton.disabled = true; importDataButton.disabled = true; importStep2.style.display = 'block'; previewLoadingIndicator.style.display = 'flex'; importErrorStep2.style.display = 'none'; previewTableContainer.style.display = 'none'; if(previewTableHead) previewTableHead.innerHTML = ''; if(previewTableBody) previewTableBody.innerHTML = ''; if(previewRowCount) previewRowCount.textContent = '';

                // Get the appropriate required columns based on import type
                let requiredCols;
                if (currentImportType === 'activity') {
                    requiredCols = requiredColumnsActivity;
                } else if (currentImportType === 'tech4ed') {
                    // For Tech4ED, only Center Name is truly required
                    requiredCols = ['Center Name'];
                } else {
                    requiredCols = requiredColumnsParticipant;
                }

                Papa.parse(selectedFile, { header: true, skipEmptyLines: true, complete: (results) => { previewLoadingIndicator.style.display = 'none'; if (results.errors.length > 0) { showImportError(`Error parsing CSV: ${results.errors[0].message}. Check row ${results.errors[0].row + 1}.`, 2); previewDataButton.disabled = false; return; } if (!results.data || results.data.length === 0) { showImportError('CSV file appears empty.', 2); previewDataButton.disabled = false; return; } const headers = results.meta.fields; const lowerCaseHeaders = headers.map(h => h.toLowerCase().trim()); const lowerCaseRequired = requiredCols.map(rc => rc.toLowerCase().trim()); const missingColumns = []; lowerCaseRequired.forEach(reqCol => { if (!lowerCaseHeaders.includes(reqCol)) { const originalCaseCol = requiredCols.find(rc => rc.toLowerCase().trim() === reqCol) || reqCol; missingColumns.push(originalCaseCol); } }); if (missingColumns.length > 0) { showImportError(`Missing required columns: ${missingColumns.join(', ')}.`, 2); previewDataButton.disabled = false; return; }

                // Skip project validation for Tech4ED DTCs
                if (currentImportType !== 'tech4ed') {
                    // Validate that all records have the correct project value
                    const currentProject = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; // <-- Use correct PHP variable
                    const projectColumnName = currentImportType === 'activity' ? 'Bureau' : 'Project';

                    // Find the actual project column name in the CSV (case-insensitive)
                    let actualProjectColumn = null;
                    for (const header of headers) {
                        if (header.toLowerCase().trim() === projectColumnName.toLowerCase().trim()) {
                            actualProjectColumn = header;
                            break;
                        }
                    }

                    if (actualProjectColumn) {
                        // Check if any rows have a different project value
                        const invalidRows = [];
                        results.data.forEach((row, index) => {
                            const rowProject = row[actualProjectColumn]?.trim();
                            if (rowProject && rowProject !== currentProject) {
                                invalidRows.push({
                                    rowIndex: index + 2, // +2 because index is 0-based and we skip header row
                                    project: rowProject
                                });
                            }
                        });

                        if (invalidRows.length > 0) {
                            // Format error message with details about invalid rows
                            let errorMsg = `This file contains data for projects other than "${currentProject}". `;
                            errorMsg += `Found ${invalidRows.length} row(s) with different project values:\n`;

                            // Show up to 5 examples
                            const examples = invalidRows.slice(0, 5);
                            examples.forEach(row => {
                                errorMsg += `- Row ${row.rowIndex}: "${row.project}"\n`;
                            });

                            if (invalidRows.length > 5) {
                                errorMsg += `...and ${invalidRows.length - 5} more.\n`;
                            }

                            errorMsg += `\nPlease upload a file containing only "${currentProject}" data.`;

                            showImportError(errorMsg, 2);
                            previewDataButton.disabled = false;
                            return;
                        }
                    }
                }

                parsedDataForImport = results.data;
                displayPreviewTable(headers, results.data);
                importDataButton.disabled = false;
                showImportMessage('Preview ready. Review data and click "Import Data".', 2);
            },
            error: (error) => {
                previewLoadingIndicator.style.display = 'none';
                showImportError(`Error reading file: ${error.message}`, 2);
                previewDataButton.disabled = false;
            }
        });
    });
}
         if (importDataButton) { importDataButton.addEventListener('click', async () => {
             if (!parsedDataForImport || parsedDataForImport.length === 0) {
                 showImportError('No data available to import.', 2);
                 return;
             }

             importDataButton.disabled = true;
             previewDataButton.disabled = true;
             importProcessingIndicator.style.display = 'flex';
             importErrorStep2.style.display = 'none';
             importResultIndicator.style.display = 'none';

             let importAction, dataKey;

             if (currentImportType === 'activity') {
                 importAction = 'importActivities';
                 dataKey = 'activities';
             } else if (currentImportType === 'tech4ed') {
                 importAction = 'importTech4ed';
                 dataKey = 'tech4ed_items';
             } else {
                 importAction = 'importParticipants';
                 dataKey = 'participants';
             }

             const currentProject = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; // <-- Use correct PHP variable

             // For Tech4ED, ensure we're sending the data properly
             let importData = {};
             if (currentImportType === 'tech4ed') {
                 importData = {
                     tech4ed_items: JSON.stringify(parsedDataForImport),
                     project: currentProject,
                     type: currentImportType
                 };
             } else {
                 importData = {
                     [dataKey]: parsedDataForImport,
                     project: currentProject,
                     type: currentImportType
                 };
             }

             const result = await performAjax(importAction, importData);

             importProcessingIndicator.style.display = 'none';
             const typeNamePlural = currentImportType === 'tech4ed' ? 'Tech4ED DTCs' : currentImportType + 's';

             if (result.success) {
                 importResultIndicator.textContent = result.message || `Successfully imported ${result.data?.imported_count || 0} ${typeNamePlural}.`;
                 importResultIndicator.className = 'success-indicator';
                 importResultIndicator.style.display = 'block';
                 showNotification(result.message || `Successfully imported ${result.data?.imported_count || 0} ${typeNamePlural}.`, 'success', 5000);

                 setTimeout(() => {
                     closeModal('import');
                     const currentSearch = new URLSearchParams(window.location.search);

                     if (currentImportType === 'activity') {
                         currentSearch.set('tab', 'activities');
                     } else if (currentImportType === 'tech4ed') {
                         currentSearch.set('tab', 'tech4ed');
                     } else {
                         currentSearch.set('tab', 'participants');
                     }

                     window.location.search = currentSearch.toString();
                 }, 2000);
             } else {
                 let errorDetails = result.data?.errors ? result.data.errors.join('\n') : 'Unknown error details.';
                 importResultIndicator.textContent = `Import failed: ${result.message || 'Unknown error'}.\n${result.data?.errors ? 'Details:\n' + errorDetails : ''}`;
                 importResultIndicator.className = 'error-indicator';
                 importResultIndicator.style.whiteSpace = 'pre-wrap';
                 importResultIndicator.style.display = 'block';
                 showNotification(`Import failed: ${result.message || 'Unknown error'}. Check modal for details.`, 'error', 7000);
                 previewDataButton.disabled = false;
                 importDataButton.disabled = false;
             }
         }); }

         // --- Tech4ED DTC Form Submission Handlers ---
         if (document.getElementById('addTech4edForm')) {
             document.getElementById('addTech4edForm').addEventListener('submit', async function(e) {
                 e.preventDefault();
                 const saveButton = document.getElementById('saveAddTech4edButton');
                 if (saveButton) {
                     saveButton.disabled = true;
                     saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                 }
                 const formData = new FormData(this);
                 formData.append('action', 'addTech4ed');
                 try {
                     const result = await performAjax('addTech4ed', Object.fromEntries(formData));
                     if (result.success) {
                         showNotification(result.message || 'Tech4ED DTC added successfully!', 'success');
                         closeModal('addTech4ed');
                         // Reload the page to show the new Tech4ED DTC
                         window.location.reload();
                     } else {
                         showNotification(`Error: ${result.message || 'Failed to add Tech4ED DTC.'}`, 'error', 5000);
                         if (saveButton) {
                             saveButton.disabled = false;
                             saveButton.innerHTML = '<i class="fas fa-save"></i> Save Tech4ED DTC';
                         }
                     }
                 } catch (error) {
                     console.error('Error adding Tech4ED DTC:', error);
                     showNotification(`Error: ${error.message || 'An unexpected error occurred.'}`, 'error', 5000);
                     if (saveButton) {
                         saveButton.disabled = false;
                         saveButton.innerHTML = '<i class="fas fa-save"></i> Save Tech4ED DTC';
                     }
                 }
             });
         }

         // Edit Tech4ED DTC Form Submission Handler
         if (document.getElementById('editTech4edForm')) {
             document.getElementById('editTech4edForm').addEventListener('submit', async function(e) {
                 e.preventDefault();
                 const saveButton = document.getElementById('saveEditTech4edButton');
                 if (saveButton) {
                     saveButton.disabled = true;
                     saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                 }
                 const formData = new FormData(this);
                 const data = Object.fromEntries(formData.entries());
                 data.id = document.getElementById('editTech4edFormId').value;

                 try {
                     const result = await performAjax('updateTech4ed', data);
                     if (result.success) {
                         showNotification(result.message || 'Tech4ED DTC updated successfully!', 'success');
                         closeModal('editTech4ed');
                         // Reload the page to show the updated Tech4ED DTC
                         window.location.reload();
                     } else {
                         showNotification(`Error: ${result.message || 'Failed to update Tech4ED DTC.'}`, 'error', 5000);
                         if (saveButton) {
                             saveButton.disabled = false;
                             saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                         }
                     }
                 } catch (error) {
                     console.error('Error updating Tech4ED DTC:', error);
                     showNotification(`Error: ${error.message || 'An unexpected error occurred.'}`, 'error', 5000);
                     if (saveButton) {
                         saveButton.disabled = false;
                         saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                     }
                 }
             });
         }

         // --- Statistics Modal Functions ---
         let currentStatKey = '';
         let currentStatData = [];
         let currentChart = null;
         let currentPage = 1;
         let rowsPerPage = 10;
         let filteredData = [];

         // Statistics Card Click Handlers
         const statCards = document.querySelectorAll('.stat-card');
         statCards.forEach(card => {
            card.addEventListener('click', async function() {
                const statKey = this.querySelector('[data-key]')?.dataset.key || this.dataset.key;
                if (!statKey) {
                    console.error('No stat key found for this card');
                    return;
                }

                const statTitle = this.querySelector('h4')?.textContent || 'Statistics Breakdown';
                openStatsModal(statKey, statTitle);
            });
         });

         // Function to open stats modal and load data (matching index.php structure)
         async function openStatsModal(statKey, statTitle) {
            // Store the current statistic key
            currentStatKey = statKey;

            // Reset current data
            currentStatData = [];
            currentPage = 1;

            // Open the modal
            openModal('statsBreakdown');

            // Update modal title
            const titleEl = document.querySelector('#statsModalTitle');
            if (titleEl) titleEl.textContent = statTitle;

            try {
                // Show loading indicator
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'block';
                document.querySelector('#statsBreakdownContent').style.display = 'none';
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'none';

                // Fetch data from server
                const result = await performAjax('getStatBreakdown', {
                    stat_key: statKey,
                    project: '<?php echo htmlspecialchars($project_filter_value); ?>'
                });

                handleStatBreakdownResult(result, statKey);
            } catch (error) {
                console.error('Error loading statistics breakdown:', error);
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';
                const errorEl = document.querySelector('#statsBreakdownErrorIndicator');
                if (errorEl) {
                    const errorMsgEl = errorEl.querySelector('span');
                    if (errorMsgEl) errorMsgEl.textContent = error.message || 'Could not load statistics data.';
                    errorEl.style.display = 'block';
                }
            }
        }

         // Function to reset modal state
         function resetStatsModal() {
            // Reset tabs
            const tabBtns = document.querySelectorAll('.stats-tab-btn');
            tabBtns.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.view === 'table');
            });

            // Show table view, hide chart view
            document.getElementById('statsTableView').style.display = 'block';
            document.getElementById('statsChartView').style.display = 'none';

            // Reset search
            const searchInput = document.getElementById('statsTableSearch');
            if (searchInput) searchInput.value = '';

            // Reset pagination
            const lengthSelect = document.getElementById('statsTableLength');
            if (lengthSelect) lengthSelect.value = '10';
            rowsPerPage = 10;
            currentPage = 1;

            // Clear table
            const tableBody = document.querySelector('#statsBreakdownTable tbody');
            if (tableBody) tableBody.innerHTML = '';
         }

         // Function to handle stat breakdown result (matching index.php structure)
         function handleStatBreakdownResult(result, statKey) {
            try {
                if (result.success && result.data) {
                    // Hide loading indicator
                    document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';

                    // Show content container
                    document.querySelector('#statsBreakdownContent').style.display = 'block';

                    // Store the current data globally
                    currentStatData = result.data;
                    filteredData = [...currentStatData];

                    // Set up table headers based on statistic type
                    setupTableHeaders(statKey);

                    // Populate table with data
                    populateStatsTable(filteredData, statKey);

                    // Set up search functionality
                    setupStatsSearch();

                    // Set up pagination controls
                    setupStatsPagination();
                } else {
                    throw new Error(result.message || 'Failed to load statistics data');
                }
            } catch (error) {
                console.error('Error loading statistics data:', error);
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'block';
                document.querySelector('#statsBreakdownErrorIndicator span').textContent = error.message || 'Could not load statistics data.';
            }
         }

        // Function to set up table headers based on statistic type (matching index.php structure)
        function setupTableHeaders(statKey) {
            const tableHeader = document.querySelector('#statsTableHeader tr');
            if (!tableHeader) return;

            let headerHTML = '';

            switch (statKey) {
                case 'activities':
                    headerHTML = `
                        <th>Activity Name</th>
                        <th>Date</th>
                        <th>Location</th>
                        <th>Lead Agency</th>
                        <th>Participants</th>
                        <th>Sectors</th>
                    `;
                    break;
                case 'participants':
                    headerHTML = `
                        <th>Activity Name</th>
                        <th>Number of Participants</th>
                    `;
                    break;
                case 'sectors':
                    headerHTML = `
                        <th>Sector Name</th>
                        <th>Activities</th>
                        <th>Participants</th>
                    `;
                    break;
                case 'agencies':
                    headerHTML = `
                        <th>Agency Name</th>
                        <th>Activities</th>
                        <th>Participants</th>
                    `;
                    break;
                case 'municipalities':
                    headerHTML = `
                        <th>Municipality Name</th>
                        <th>Activities</th>
                        <th>Participants</th>
                        <th>Barangays</th>
                    `;
                    break;
                case 'barangays':
                    headerHTML = `
                        <th>Barangay Name</th>
                        <th>Municipality</th>
                        <th>Activities</th>
                        <th>Participants</th>
                    `;
                    break;
                case 'district1':
                case 'district2':
                    headerHTML = `
                        <th>Activity Name</th>
                        <th>Date</th>
                        <th>Location</th>
                        <th>Lead Agency</th>
                        <th>Participants</th>
                        <th>Sectors</th>
                    `;
                    break;
                default:
                    headerHTML = `
                        <th>NAME</th>
                        <th>COUNT</th>
                        <th>PERCENTAGE</th>
                    `;
                    break;
            }

            tableHeader.innerHTML = headerHTML;
        }

         // Function to populate stats table (matching index.php structure)
         function populateStatsTable(data, statKey) {
            const tableBody = document.querySelector('#statsBreakdownTable tbody');
            if (!tableBody) return;

            // Store the original data
            filteredData = [...data];

            // Calculate pagination
            const startIndex = (currentPage - 1) * rowsPerPage;
            const endIndex = startIndex + rowsPerPage;
            const paginatedData = data.slice(startIndex, endIndex);

            // Clear existing rows
            tableBody.innerHTML = '';

            // Add rows based on statistic type
            paginatedData.forEach(item => {
                const row = document.createElement('tr');
                let rowHTML = '';

                switch (statKey) {
                    case 'activities':
                        rowHTML = `
                            <td>${escapeHtml(item.activity_name || '')}</td>
                            <td>${escapeHtml(item.date_of_activity || '')}</td>
                            <td>${escapeHtml(item.location || '')}</td>
                            <td>${escapeHtml(item.lead_agency || '')}</td>
                            <td>${parseInt(item.number_of_participants || 0).toLocaleString()}</td>
                            <td>${escapeHtml(item.sectors_involved || '')}</td>
                        `;
                        break;
                    case 'participants':
                        rowHTML = `
                            <td>${escapeHtml(item.activity_name || '')}</td>
                            <td>${parseInt(item.number_of_participants || 0).toLocaleString()}</td>
                        `;
                        break;
                    case 'sectors':
                        rowHTML = `
                            <td>${escapeHtml(item.sector_name || '')}</td>
                            <td>${parseInt(item.activities_count || 0).toLocaleString()}</td>
                            <td>${parseInt(item.participants_count || 0).toLocaleString()}</td>
                        `;
                        break;
                    case 'agencies':
                        rowHTML = `
                            <td>${escapeHtml(item.agency_name || '')}</td>
                            <td>${parseInt(item.activities_count || 0).toLocaleString()}</td>
                            <td>${parseInt(item.participants_count || 0).toLocaleString()}</td>
                        `;
                        break;
                    case 'municipalities':
                        rowHTML = `
                            <td>${escapeHtml(item.municipality_name || '')}</td>
                            <td>${parseInt(item.activities_count || 0).toLocaleString()}</td>
                            <td>${parseInt(item.participants_count || 0).toLocaleString()}</td>
                            <td>${parseInt(item.barangays_count || 0).toLocaleString()}</td>
                        `;
                        break;
                    case 'barangays':
                        rowHTML = `
                            <td>${escapeHtml(item.barangay_name || '')}</td>
                            <td>${escapeHtml(item.municipality_name || '')}</td>
                            <td>${parseInt(item.activities_count || 0).toLocaleString()}</td>
                            <td>${parseInt(item.participants_count || 0).toLocaleString()}</td>
                        `;
                        break;
                    case 'district1':
                    case 'district2':
                        rowHTML = `
                            <td>${escapeHtml(item.activity_name || '')}</td>
                            <td>${escapeHtml(item.date_of_activity || '')}</td>
                            <td>${escapeHtml(item.location || '')}</td>
                            <td>${escapeHtml(item.lead_agency || '')}</td>
                            <td>${parseInt(item.number_of_participants || 0).toLocaleString()}</td>
                            <td>${escapeHtml(item.sectors_involved || '')}</td>
                        `;
                        break;
                    default:
                        // Default format for backward compatibility
                        const total = data.reduce((sum, dataItem) => sum + parseInt(dataItem.count || 0), 0);
                        const percentage = total > 0 ? ((item.count / total) * 100).toFixed(1) : '0.0';
                        rowHTML = `
                            <td>${escapeHtml(item.name || '')}</td>
                            <td>${parseInt(item.count || 0).toLocaleString()}</td>
                            <td>${percentage}%</td>
                        `;
                        break;
                }

                row.innerHTML = rowHTML;
                tableBody.appendChild(row);
            });

            // Update pagination info and buttons separately (matching index.php exactly)
            updateStatsPaginationInfo(data.length);
            updateStatsPaginationButtons(data.length);
         }

         // Function to update pagination info
         function updateStatsPaginationInfo(totalItems) {
            const startIndex = (currentPage - 1) * rowsPerPage + 1;
            const endIndex = Math.min(currentPage * rowsPerPage, totalItems);

            // Update the new element IDs from freewifi4all.php structure
            const startEl = document.getElementById('statsTableStart');
            const endEl = document.getElementById('statsTableEnd');
            const totalEl = document.getElementById('statsTableTotal');

            if (startEl) startEl.textContent = totalItems > 0 ? startIndex : 0;
            if (endEl) endEl.textContent = totalItems > 0 ? endIndex : 0;
            if (totalEl) totalEl.textContent = totalItems;
         }

         // Function to update pagination buttons (matching freewifi4all.php exactly)
         function updateStatsPaginationButtons(totalItems) {
            const totalPages = Math.ceil(totalItems / rowsPerPage);
            const prevButton = document.getElementById('statsTablePrevious');
            const nextButton = document.getElementById('statsTableNext');
            const pageNumbersContainer = document.getElementById('statsTablePageNumbers');

            // Update previous button
            if (prevButton) {
                prevButton.disabled = currentPage <= 1;
            }

            // Update next button
            if (nextButton) {
                nextButton.disabled = currentPage >= totalPages;
            }

            // Update page numbers
            if (pageNumbersContainer) {
                pageNumbersContainer.innerHTML = '';

                // Always show page numbers, even for single page (like freewifi4all.php)
                if (totalPages >= 1) {
                    const maxVisiblePages = 5;
                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    // Adjust start page if we're near the end
                    if (endPage - startPage < maxVisiblePages - 1) {
                        startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    // First page and ellipsis if needed
                    if (startPage > 1) {
                        const firstPageBtn = document.createElement('a');
                        firstPageBtn.href = "#";
                        firstPageBtn.className = 'page-number';
                        firstPageBtn.textContent = '1';
                        firstPageBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            currentPage = 1;
                            populateStatsTable(filteredData, currentStatKey);
                        });
                        pageNumbersContainer.appendChild(firstPageBtn);

                        if (startPage > 2) {
                            const ellipsis = document.createElement('span');
                            ellipsis.className = 'page-number disabled';
                            ellipsis.textContent = '...';
                            pageNumbersContainer.appendChild(ellipsis);
                        }
                    }

                    // Add page number buttons
                    for (let i = startPage; i <= endPage; i++) {
                        const pageButton = document.createElement('a');
                        pageButton.href = "#";
                        pageButton.className = i === currentPage ? 'page-number active' : 'page-number';
                        pageButton.textContent = i;
                        pageButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            currentPage = i;
                            populateStatsTable(filteredData, currentStatKey);
                        });
                        pageNumbersContainer.appendChild(pageButton);
                    }

                    // Last page and ellipsis if needed
                    if (endPage < totalPages) {
                        if (endPage < totalPages - 1) {
                            const ellipsis = document.createElement('span');
                            ellipsis.className = 'page-number disabled';
                            ellipsis.textContent = '...';
                            pageNumbersContainer.appendChild(ellipsis);
                        }

                        const lastPageBtn = document.createElement('a');
                        lastPageBtn.href = "#";
                        lastPageBtn.className = 'page-number';
                        lastPageBtn.textContent = totalPages;
                        lastPageBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            currentPage = totalPages;
                            populateStatsTable(filteredData, currentStatKey);
                        });
                        pageNumbersContainer.appendChild(lastPageBtn);
                    }
                }
            }
         }

         // Function to set up search functionality (matching index.php structure)
         function setupStatsSearch() {
            const searchInput = document.getElementById('statsTableSearch');
            if (searchInput && !searchInput.hasAttribute('data-search-setup')) {
                searchInput.setAttribute('data-search-setup', 'true');
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filteredData = currentStatData.filter(item => {
                        switch (currentStatKey) {
                            case 'activities':
                            case 'district1':
                            case 'district2':
                                return (item.activity_name || '').toLowerCase().includes(searchTerm) ||
                                       (item.location || '').toLowerCase().includes(searchTerm) ||
                                       (item.lead_agency || '').toLowerCase().includes(searchTerm) ||
                                       (item.sectors_involved || '').toLowerCase().includes(searchTerm);
                            default:
                                return (item.name || '').toLowerCase().includes(searchTerm);
                        }
                    });
                    currentPage = 1;
                    populateStatsTable(filteredData, currentStatKey);
                });
            }
         }

         // Function to set up pagination controls (matching index.php structure)
         function setupStatsPagination() {
            const rowsPerPageSelect = document.getElementById('statsTableLength');
            const prevBtn = document.getElementById('statsTablePrevious');
            const nextBtn = document.getElementById('statsTableNext');

            // Clear any existing event listeners by cloning and replacing elements
            if (rowsPerPageSelect && !rowsPerPageSelect.hasAttribute('data-pagination-setup')) {
                rowsPerPageSelect.setAttribute('data-pagination-setup', 'true');
                rowsPerPageSelect.addEventListener('change', function() {
                    rowsPerPage = parseInt(this.value);
                    currentPage = 1;
                    populateStatsTable(filteredData, currentStatKey);
                });
            }

            if (prevBtn && !prevBtn.hasAttribute('data-pagination-setup')) {
                prevBtn.setAttribute('data-pagination-setup', 'true');
                prevBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        populateStatsTable(filteredData, currentStatKey);
                    }
                });
            }

            if (nextBtn && !nextBtn.hasAttribute('data-pagination-setup')) {
                nextBtn.setAttribute('data-pagination-setup', 'true');
                nextBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
                    if (currentPage < totalPages) {
                        currentPage++;
                        populateStatsTable(filteredData, currentStatKey);
                    }
                });
            }
         }

         // Function to set up tabs in stats modal (matching freewifi4all.php exactly)
         function setupStatsTabs() {
            const tabBtns = document.querySelectorAll('.stats-tab-btn');
            const tableView = document.getElementById('statsTableView');
            const chartView = document.getElementById('statsChartView');

            // Remove existing event listeners by cloning and replacing buttons
            tabBtns.forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
            });

            // Get fresh references to the buttons
            const freshTabBtns = document.querySelectorAll('.stats-tab-btn');

            freshTabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.dataset.view;

                    // Update active tab
                    freshTabBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    // Show/hide views
                    if (view === 'table') {
                        if (tableView) tableView.style.display = 'block';
                        if (chartView) chartView.style.display = 'none';
                    } else if (view === 'chart') {
                        if (tableView) tableView.style.display = 'none';
                        if (chartView) chartView.style.display = 'block';
                        // Initialize chart when switching to chart view
                        initializeStatsChart(currentStatData);
                    }
                });
            });

            // Set up chart type buttons
            setupChartTypeButtons();
         }

         // Function to set up chart type buttons (matching freewifi4all.php exactly)
         function setupChartTypeButtons() {
            const chartTypeBtns = document.querySelectorAll('.chart-type-btn');

            // Remove existing event listeners by cloning and replacing buttons
            chartTypeBtns.forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
            });

            // Get fresh references to the buttons
            const freshChartTypeBtns = document.querySelectorAll('.chart-type-btn');

            freshChartTypeBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Skip if already active
                    if (btn.classList.contains('active')) return;

                    // Update active state for all buttons
                    freshChartTypeBtns.forEach(b => {
                        b.classList.remove('active');
                        b.style.backgroundColor = '#fff';
                        b.style.color = '#333';
                    });

                    // Set active state for clicked button
                    btn.classList.add('active');
                    btn.style.backgroundColor = 'var(--primary-color)';
                    btn.style.color = 'white';

                    // Get selected chart type
                    const selectedChartType = btn.dataset.type;

                    // Update the chart with the new type
                    initializeStatsChart(currentStatData, selectedChartType);
                });
            });
         }

         // Function to initialize stats chart (matching freewifi4all.php exactly)
         function initializeStatsChart(data, forceChartType = null) {
            // Store data in global variable if not already set
            if (data && data.length > 0) {
                currentStatData = data;
            }

            // Update summary information
            updateChartSummary(currentStatData);

            // Determine chart type
            let chartType = forceChartType;
            if (!chartType) {
                // Get active chart type button
                const activeChartTypeBtn = document.querySelector('.chart-type-btn.active');
                if (activeChartTypeBtn) {
                    chartType = activeChartTypeBtn.dataset.type;
                } else {
                    chartType = 'doughnut'; // Default to doughnut
                }
            }

            // Prepare chart data
            const labels = currentStatData.map(item => item.name);
            const values = currentStatData.map(item => item.count);
            const backgroundColors = generateChartColors(currentStatData.length);

            // Create chart configuration
            const config = createChartConfig(chartType, labels, values, backgroundColors);

            // Get chart canvas
            const canvas = document.getElementById('statsChart');
            if (!canvas) return;

            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }

            // Create new chart
            currentChart = new Chart(canvas, config);
         }

         // Function to update chart summary information (matching freewifi4all.php exactly)
         function updateChartSummary(data) {
            const totalValue = data.reduce((sum, item) => sum + item.count, 0);
            const itemCount = data.length;

            // Find top item (item with highest count)
            let topItem = { name: 'None', count: 0 };
            if (data.length > 0) {
                topItem = data.reduce((max, item) => item.count > max.count ? item : max, data[0]);
            }

            // Update summary elements
            document.getElementById('chartTotalValue').textContent = totalValue.toLocaleString();
            document.getElementById('chartItemCount').textContent = itemCount.toLocaleString();

            // Format top item text with percentage
            const topItemPercentage = totalValue > 0 ? ((topItem.count / totalValue) * 100).toFixed(1) : '0.0';
            document.getElementById('chartTopItem').textContent = `${topItem.name} (${topItemPercentage}%)`;
         }

         // Function to create chart configuration based on chart type (from freewifi4all.php)
         function createChartConfig(chartType, labels, values, backgroundColors) {
            // Common tooltip callback
            const tooltipCallback = {
                label: function(context) {
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const value = context.raw;
                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                    return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
                }
            };

            // Base configuration
            const baseConfig = {
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: backgroundColors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: tooltipCallback.label
                            }
                        }
                    }
                }
            };

            if (chartType === 'doughnut') {
                return {
                    type: 'doughnut',
                    ...baseConfig,
                    options: {
                        ...baseConfig.options,
                        plugins: {
                            ...baseConfig.options.plugins,
                            legend: {
                                position: 'right',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                };
            } else if (chartType === 'bar') {
                return {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Count',
                            data: values,
                            backgroundColor: backgroundColors,
                            borderWidth: 0, // Remove border for solid bars
                            borderRadius: 0, // Remove border radius for rectangular bars
                            barPercentage: 0.8,
                            categoryPercentage: 0.9
                        }]
                    },
                    options: {
                        ...baseConfig.options,
                        indexAxis: 'y', // Always use horizontal bar chart
                        plugins: {
                            ...baseConfig.options.plugins,
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: true,
                                displayColors: false,
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: {
                                    weight: 'bold'
                                },
                                callbacks: {
                                    title: function(context) {
                                        return context[0].label;
                                    },
                                    label: function(context) {
                                        const value = context.raw;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
                                        return `${context.dataset.label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    precision: 0
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                };
            }

            return baseConfig;
         }

         // Function to generate chart colors (matching freewifi4all.php exactly)
         function generateChartColors(count) {
            const colors = [
                '#6A5AE0', '#36A2EB', '#4BC0C0', '#9966FF', '#FF9F40',
                '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384', '#36A2EB',
                '#FFCD56', '#FF9F40', '#9966FF', '#C9CBCF', '#4BC0C0'
            ];

            const result = [];
            for (let i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
         }



         // --- Initial UI State ---
         console.log('JavaScript loaded successfully. Chart.js available:', typeof Chart);
         console.log('Current active tab:', currentActiveTab);
         console.log('Report year:', currentReportYear);

         updateActionBar();
         // Fetch graphs if the report tab is active on initial load
         if (currentActiveTab === 'reports') {
             console.log('Initial load: Reports tab is active, calling fetchAndRenderGraphs');
             fetchAndRenderGraphs();
         } else {
             console.log('Initial load: Active tab is', currentActiveTab);
         }

       }); // End DOMContentLoaded
    </script>
    <script src="js/filter-dropdown.js"></script>
    <script src="js/settings.js"></script>

    <!-- Statistics Breakdown Modal (matching index.php structure) -->
    <div id="statsBreakdownModal" class="modal large">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-chart-bar"></i> <span id="statsModalTitle">Statistics Breakdown</span></h2>
                <button type="button" class="close-modal" data-modal-id="statsBreakdown">&times;</button>
            </div>
            <div class="modal-body">
                <div id="statsBreakdownLoadingIndicator" class="loading-indicator active">
                    <i class="fas fa-spinner fa-spin"></i> Loading statistics data...
                </div>
                <div id="statsBreakdownErrorIndicator" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load statistics data.</span>
                </div>
                <div id="statsBreakdownContent" style="display: none;">
                    <div class="stats-table-controls">
                        <div class="stats-table-length">
                            Show
                            <select id="statsTableLength">
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            entries
                        </div>
                        <div class="stats-table-search">
                            <input type="search" id="statsTableSearch" placeholder="Search...">
                        </div>
                    </div>
                    <div class="stats-table-container">
                        <table id="statsBreakdownTable" class="stats-table">
                            <thead id="statsTableHeader">
                                <tr>
                                    <th>NAME</th>
                                    <th>COUNT</th>
                                    <th>PERCENTAGE</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="stats-table-pagination">
                        <div class="stats-table-info">
                            Showing <span id="statsTableStart">1</span> to <span id="statsTableEnd">10</span> of <span id="statsTableTotal">0</span> entries
                        </div>
                        <div class="stats-table-pages">
                            <button id="statsTablePrevious" class="pagination-btn" disabled>Previous</button>
                            <div id="statsTablePageNumbers" class="pagination-numbers"></div>
                            <button id="statsTableNext" class="pagination-btn">Next</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="statsBreakdown">Close</button>
            </div>
        </div>
    </div>

    <?php
      // Include common modals
      include 'modals.php';

      // Output access control JavaScript if user has limited access
      echo generateAccessControlJS($hasFullAccess);

      // Close DB connection if still open
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>
